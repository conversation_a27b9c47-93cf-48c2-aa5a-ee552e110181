# WMS20250620.py 序号排序修复任务

## 问题描述
序号列显示顺序错误：1, 10, 11, 2, 3, 4, 5, 6, 7, 8, 9
原因：序号作为字符串排序，导致字典序排序而非数字排序

## 解决方案（修订）
最终采用SQL ROW_NUMBER()方案：在数据库查询中直接生成数字序号
- 数据库生成：1, 2, 3, 10, 11...（数字类型）
- 显示效果：1, 2, 3, 10, 11...（正常数字显示）
- 排序结果：1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11...（正确的数字排序）
- 优势：代码简洁，与进销存窗口排序逻辑统一

## 修复计划（修订）
1. 修改4个SQL查询，使用ROW_NUMBER()生成序号
2. 删除Python中的序号生成逻辑
3. 删除自定义代理类和相关设置
4. 修复数据索引引用
5. 验证功能正常

## 修改文件
- WMS20250620.py

## 修改位置
- 第614行：get_chaxun_dd_data() 函数
- 第640行：get_chaxun_PF_PHLRHZ_data() 函数  
- 第710行：get_chaxun_PF_PHLRMX_data() 函数
- 第735行：get_chaxun_DBMX_KP_data() 函数
- 第2103行附近：添加 SequenceNumberDelegate 类
- 第2238行附近：为表格序号列设置代理

## 执行状态
- [x] 创建 SequenceNumberDelegate 类
- [x] 修改序号生成逻辑
- [x] 应用代理到表格
- [ ] 测试验证

## 修改详情
1. 创建了 SequenceNumberDelegate 类（第2102-2111行）
   - 重写 displayText 方法，将"001"转换显示为"1"
   - 处理转换异常，确保稳定性

2. 修改了4个数据获取函数的序号生成：
   - get_chaxun_dd_data() 第614行
   - get_chaxun_PF_PHLRHZ_data() 第640行
   - get_chaxun_PF_PHLRMX_data() 第710行
   - get_chaxun_DBMX_KP_data() 第735行
   - 将 str(row_idx + 1) 改为 f"{row_idx + 1:03d}"

3. 为所有表格序号列设置代理：
   - tree1 第2249-2250行
   - tree2 第2273-2274行
   - tree3 第2293-2294行
   - tree4 第2313-2314行
