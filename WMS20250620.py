import xlsxwriter
import os
import sys
import time
from PyQt5.QtWidgets import *
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QFile, QTextStream, QSize, QEvent # 添加 QEvent 导入
from PyQt5.QtGui import QStandardItemModel, QStandardItem, QColor, QIcon, QPixmap, QPainter, QFont, QPalette
from PyQt5.QtSvg import QSvgRenderer # 新增 QtSvg 模块导入
import pymssql
import win32gui
import win32api
import win32print
import win32com.client as win32
import tkinter
from tkinter import ttk
import decimal
from datetime import date, datetime
import datetime as dt
import base64
import traceback
# import base64 # Redundant import removed
# 添加警告过滤器来忽略 DeprecationWarning
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message="QVector.*")

# 添加QTableWidget相关的导入
from PyQt5.QtWidgets import (QTableWidget, QTableWidgetItem, QAbstractItemView,
                           QHeaderView, QStyledItemDelegate, QStyle)

# --- 设置 AppUserModelID (仅限 Windows) ---
import ctypes
import platform
if platform.system() == "Windows": # 或者 sys.platform == "win32"
    try:
        myappid = u'XHMYY.WMS.Monitor.1.0' # 请替换为你的唯一 ID
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
        print(f"AppUserModelID set to: {myappid}")
    except AttributeError:
        print("警告: 无法设置 AppUserModelID (ctypes.windll.shell32 可能不存在或属性缺失)。")
    except Exception as e:
        print(f"设置 AppUserModelID 时出错: {e}")
# --- 结束 AppUserModelID 设置 ---

# 全局变量，用于保存进销存窗口实例，防止被垃圾回收
kucun_dialog_instance = None

ExcelApp = win32.Dispatch("Excel.Application")
speak = win32.Dispatch("SAPI.SpVoice")
speak.Speak("欢迎使用")

# 连接数据库获取并返回数据
def sjcx_wms(event, params=None):
    conn = None
    try:
        conn = pymssql.connect(host='***********',
                               user='sa',
                               password='xhmyy_2018',
                               database='yqwms_new',
                               charset='GBK',
                               timeout=15) # Added timeout
        cursor = conn.cursor()
        if params:
            cursor.execute(event, params)
        else:
            cursor.execute(event)
        rs_temp = cursor.fetchall()
        return rs_temp
    except pymssql.OperationalError as e:
        print(f'数据库操作错误 (OperationalError) in sjcx_wms: {e}')
        print(f"SQL: {event}, Params: {params}")
        return []
    except pymssql.ProgrammingError as e:
        print(f'数据库编程错误 (ProgrammingError) in sjcx_wms: {e}')
        problem_sql_display = event
        if params:
            try:
                problem_sql_display = event % tuple(repr(p) for p in params)
            except TypeError:
                 problem_sql_display = f"{event} WITH PARAMS {params}"
        print(f'Problem SQL (sjcx_wms): {problem_sql_display}')
        return []
    except pymssql.Error as e: # Catch other pymssql errors
        print(f'数据库错误 (pymssql.Error) in sjcx_wms: {e}')
        print(f"SQL: {event}, Params: {params}")
        return []
    except Exception as e:
        print(f'sjcx_wms 执行时发生未知错误: {e}')
        print(f"SQL: {event}, Params: {params}")
        traceback.print_exc()
        return []
    finally:
        if conn:
            try:
                conn.close()
            except Exception as e:
                print(f"Error closing connection in sjcx_wms: {e}")

# 创建一个自定义QDialog类，用于弹出窗口
class PopupDialog(QDialog):
    def __init__(self, parent=None, update_thread=None):
        super().__init__(parent)
        self.update_thread = update_thread  # 传入更新线程，以便在窗口关闭时停止线程

    # 当窗口关闭时触发这个函数
    def closeEvent(self, event):
        if self.update_thread is not None:
            self.update_thread.stop_thread = True  # 设置标志位，停止线程
        event.accept()  # 接受关闭事件

# 创建一个更新QTreeView的线程类
class UpdateTreeViewThread(QThread):
    update_signal = pyqtSignal()  # 修改：信号不再传递数据

    def __init__(self):
        super().__init__()
        self.stop_thread = False

    # 线程运行函数
    def run(self):
        while not self.stop_thread:
            try:
                self.update_signal.emit() # 只发射请求信号
                time.sleep(3) # Restored original sleep time
            except Exception as e:
                print(f"Error in UpdateTreeViewThread: {e}")
                traceback.print_exc()

# 创建一个更新Window的线程类
class UpdateVoiceThread(QThread):
    update_signal = pyqtSignal()  # 定义一个信号，用于在线程中更新UI

    def __init__(self, checkbox_voice_alert): # 修改：接收勾选框对象
        super().__init__()
        self.stop_thread = False
        self.checkbox_voice_alert = checkbox_voice_alert # 修改：保存勾选框对象的引用

    # 线程运行函数
    def run(self):
        while not self.stop_thread:
            try:
                if not self.checkbox_voice_alert.isChecked(): # 修改：在线程中检查勾选框状态
                    VoiceAlert()
                self.update_signal.emit()
                time.sleep(16)
            except Exception as e:
                print(f"Error in UpdateVoiceThread: {e}")
                traceback.print_exc()

# checkbox 状态变化时的处理函数
# def on_checkbox_stateChanged(state):
#     if state:  # 如果 checkbox 被选中
#         print('111')
#         update_thread.start()  # 开始线程
#     else:  # 如果 checkbox 被取消选中
#         print('2222')
#         update_thread.requestInterruption()  # 请求终止线程
def on_checkbox_stateChanged(state):
    if checkbox2.isChecked():  # 如果"自动刷新"被选中
        update_thread.stop_thread = False
        if not update_thread.isRunning():
            update_thread.start()
    else:
        update_thread.stop_thread = True

# 拣货单 结果输出到EXCEL表
def output(x, y):
    data = x
    # table_head = ['架位号', '商品名称', '规格', '批号', '生产厂家', '数量', '单位', '整件']

    table_head = ['商品编号', '架位号', '商品名称', '规格', '批号', '生产日期', '有效期', '生产厂家', '数量', '单位', '整件']
                    # 0            1         2         3       4        5          6         7         8       9       10


    wb = xlsxwriter.Workbook('\\\\*************\\wms拣货单\\拣货单' + y + str(l[8]) + '  ' + l[0].strip() + '.xlsx')
    ws = wb.add_worksheet()
    # ws_style = wb.add_format({'bold': True, 'border': 1, 'align': 'left', 'valign': 'vcenter', 'text_wrap': True, })
    ws_style = wb.add_format({'font': 'Yahei', 'font_size': '12', 'border': 1, 'align': 'left', 'valign': 'vjustify', 'text_wrap': True, })
    # big_style = wb.add_format({'font_size': '16'})

    ws.set_landscape()   # 设置页面为横向（横向打印）
    ws.set_margins(left=0.2, right=0.1)
    ws.set_row(0, 30)
    ws.set_column('A:A', 12)
    ws.set_column('B:B', 14)
    ws.set_column('C:C', 16)
    ws.set_column('D:D', 12)
    ws.set_column('E:E', 15)
    ws.set_column('F:F', 12)
    ws.set_column('G:G', 12)
    ws.set_column('H:H', 16)
    ws.set_column('I:I', 6)
    ws.set_column('J:J', 5)
    ws.set_column('K:K', 5)
    # ws.set_column('I:I', 5)

    ws.write_row('A1', table_head, ws_style)
    for row in range(0, len(data)):
        ws.write_row(row+1, 0, data[row], ws_style)

    # ws.set_column('G:G', cell_format=big_style)

    # header1 = '&L &16' + l[0].strip() + '&C &16' + l[1].strip() + '&R &20' + l[2].strip()
    # footer1 = '&L &12 ' + str(l[8]) + '     ' + l[0].strip() + '&C &12 第&P页' + '/共&N页' + '&R' + str(l[3]).strip() + '       ' + l[7].strip()
    header1 = '&L &12第&P页' + '/共&N页' + '&C &16' + l[1].strip() + '&R &20' + ':' + l[2].strip()
    footer1 = '&L &16 ' + str(l[8]) + '&C &16 ' + l[0].strip() + '&R' + str(l[3]).strip() + '       ' + l[7].strip()

    ws.set_header(header1, {"margin": 0.3})
    ws.set_footer(footer1, {"margin": 0.5})

    wb.close()

def PRT():
    print(l)
    print(l[-3])
    # 读取拣货单列表
    if l[-2] == '是':
        try:
            filename = '\\\\*************\\wms拣货单\\拣货单' + str(l[8]) + '  ' + l[0].strip() + '.xlsx'
            f = open(filename, "r", encoding='utf8')
            f.close()
            QMessageBox.information(window, '提示', '已经打印过拣货单，请勿重复打印！')
        except FileNotFoundError:
            print('PRT')
            # output(list_prt, '')
            # path = '\\\\*************\\wms拣货单\\拣货单' + str(l[8]) + '  ' + l[0].strip() + '.xlsx'
            # win32api.ShellExecute(0, "print", path, '/d:"%s"' % win32print.GetDefaultPrinter(), ".", 0)

            # hwnd = win32gui.GetForegroundWindow()                               # 获取当前活动窗口的句柄
            # excel = win32.GetActiveObject("Excel.Application")                  # 获取 Excel 应用程序对象并关闭应用程序
            # excel.Quit()
            # win32gui.SetForegroundWindow(hwnd)                                  # 将活动窗口显示在最前面
            # shua()
            try:
                output(list_prt, '')
                path = '\\\\*************\\wms拣货单\\拣货单' + str(l[8]) + '  ' + l[0].strip() + '.xlsx'
                win32api.ShellExecute(0, "print", path, '/d:"%s"' % win32print.GetDefaultPrinter(), ".", 0)

                excel = win32.GetActiveObject("Excel.Application")
                excel.Quit()
            except Exception as e:
                QMessageBox.warning(window, '错误', f'打印过程中出现错误: {str(e)}')
            finally:
                try:
                    hwnd = win32gui.GetForegroundWindow()
                    win32gui.SetForegroundWindow(hwnd)
                except Exception:
                    pass  # 忽略设置前台窗口的错误
    else:
        QMessageBox.information(window, '提示', '请刷新单据确认完成 补货 和 任务调度 以后再打印！')

def VIEW():
    # 读取拣货单列表
    try:
        filename = '\\\\*************\\wms拣货单\\拣货单' + str(l[8]) + '  ' + l[0].strip() + '.xlsx'
        f = open(filename, "r", encoding='utf8')
        f.close()
        QMessageBox.information(window, '提示', '已经打印过拣货单，请勿重复打印！')

    except FileNotFoundError:
        print('VIEW')
        output(list_prt, '')
        path = '\\\\*************\\wms拣货单\\拣货单' + str(l[8]) + '  ' + l[0].strip() + '.xlsx'
        win32api.ShellExecute(0, "open", path, '', ".", 1)
        shua()

def OPEN():
    directory = '\\\\*************\\wms拣货单'
    os.system("explorer.exe %s" % directory)

def dtpdb():
    # list_sphwph_dp_all = list()
    list_sphwph_dp = list()
    list_sphwph_dp_AB = list()
    list_sphwph_dp_CT = list()
    res_date = sjcx_wms(event='SELECT CONVERT(varchar(100), GETDATE(), 23)')  # 查询当前日期
def dtpdb():
    # list_sphwph_dp_all = list()
    list_sphwph_dp = list()
    list_sphwph_dp_AB = list()
    list_sphwph_dp_CT = list()
    res_date = sjcx_wms(event='SELECT CONVERT(varchar(100), GETDATE(), 23)')  # 查询当前日期
    list_splsk_all = sjcx_wms(event="select distinct hw, spid from splsk where rq='" + res_date[0][0] + "'order by hw")
    # list_splsk_all = sjcx(event="select distinct hw, spid from splsk where rq='2023-04-17'order by hw")

    list_sphwph_all = sjcx_wms(
        event="select sphwph.miejph, wms_spkfk.spbh, wms_spkfk.spmch, wms_spkfk.shpgg, sphwph.pihao, wms_spkfk.shpchd, wms_spkfk.jlgg, sphwph.shl, wms_spkfk.dw, sphwph.spid from sphwph, wms_spkfk where sphwph.spid = wms_spkfk.spid order by SPHWPH.miejph ")
    # for j in list_sphwph_all:
    #     if (j[0], j[-1]) in list_splsk_all:  # and j[5] != 0:
    #         list_sphwph_dp_all.append((j[0], j[1], j[2], j[3], j[4], j[5], j[6], j[7], j[8]))    #动过的  货位和 spid 加入 list_sphwph_dp_all
    #         # elif (j[0], j[-1]) in list_splsk_A:
    #         if j[7] != 0:
    #             # list_sphwph_dp.append((j[0], j[1]))                                              #动过的  货位和 spbh  其中不为0库存的  加入 list_sphwph_dp
    #             list_sphwph_dp.append((j[0], j[1], j[2], j[3], j[4], j[5], j[6], j[7], j[8]))

    for j in list_sphwph_all:
        if (j[0], j[-1]) in list_splsk_all and j[7] != 0:
            list_sphwph_dp.append((j[0], j[1], j[2], j[3], j[4], j[5], j[6], j[7], j[8]))
    print(len(list_sphwph_dp))
    #     if (k[0], k[1]) in list_sphwph_dp and k[7] == 0:
    #         list_sphwph_dp_all.remove(k)                                                        # 将 list_sphwph_dp_all 中的   （货位 和 spbh） 在 list_sphwph_dp 中有记录 且 shl为0  的项删除
            # print(k)
            # time.sle ep(3)

    # for x in list_sphwph_dp_all:
    for x in list_sphwph_dp:
        if x[0][0] == 'A' or x[0][0] == 'B':
            list_sphwph_dp_AB.append(x)
        else:
            list_sphwph_dp_CT.append(x)

    # 结果输出到EXCEL表
    def output(x, y):
        data = x
        table_head = ['架位号', '商品编号', '商品名称', '规格', '批次号', '厂家', '件装量', '数量', '单位']
        # wb = xlsxwriter.Workbook('\\\\*************\\WMS动盘表\\动态盘点表' + res_date[0][0] + '.xlsx')
        wb = xlsxwriter.Workbook(r'c:\WMS动盘表\动态盘点表' + y + res_date[0][0] + '.xlsx')
        ws = wb.add_worksheet()
        ws_style = wb.add_format({'font': '宋体', 'font_size': '11', 'border': 1, })
        ws.set_default_row(17)
        ws.set_column('A:A', 11)
        ws.set_column('B:B', 7)
        ws.set_column('C:C', 17)
        ws.set_column('D:D', 10)
        ws.set_column('E:E', 14)
        ws.set_column('F:F', 8)
        ws.set_column('G:G', 5)
        ws.set_column('H:H', 5)
        ws.set_column('I:I', 4)

        ws.write_row('A1', table_head, ws_style)
        for row in range(0, len(data)):
            ws.write_row(row + 1, 0, data[row], ws_style)

        header1 = '&C &12 ' + y + '  ' + '第&P页，共&N页'
        footer1 = '&C &12 动态盘点表' + ' ' + res_date[0][0]

        ws.set_header(header1, {"margin": 0.3})
        ws.set_footer(footer1, {"margin": 0.5})

        wb.close()

    # res_date = sjcx(event='SELECT CONVERT(varchar(100), GETDATE(), 23)')  # 查询当前日期

    def AB():
        print('A-B')
        output(list_sphwph_dp_AB, 'A-B')
        # path = '\\\\*************\\WMS动盘表\\动态盘点表' + 'A-B' + res_date[0][0] + '.xlsx'
        path = r'c:\WMS动盘表\动态盘点表' + 'A-B' + res_date[0][0] + '.xlsx'
        win32api.ShellExecute(0, "print", path, '/d:"%s"' % win32print.GetDefaultPrinter(), ".", 0)

    def CT():
        print('C-T')
        output(list_sphwph_dp_CT, 'C-T')
        # path = '\\\\*************\\WMS动盘表\\动态盘点表' + 'C-T' + res_date[0][0] + '.xlsx'
        path = r'c:\WMS动盘表\动态盘点表' + 'C-T' + res_date[0][0] + '.xlsx'
        win32api.ShellExecute(0, "print", path, '/d:"%s"' % win32print.GetDefaultPrinter(), ".", 0)

    def ALL():
        print('ALL')
        output(list_sphwph_dp, ' ')
        # path = '\\\\*************\\WMS动盘表\\动态盘点表' + res_date[0][0] + '.xlsx'
        path = r'c:\WMS动盘表\动态盘点表' + ' ' + res_date[0][0] + '.xlsx'
        win32api.ShellExecute(0, "open", path, '', ".", 1)

    win_dpb = PopupDialog(window)
    dpt_layout = QVBoxLayout()
    dpt_layout.addWidget(QPushButton("A-B区打印", clicked=AB))
    dpt_layout.addWidget(QPushButton("C-T区打印", clicked=CT))
    dpt_layout.addWidget(QPushButton("打开预览", clicked=ALL))
    win_dpb.setLayout(dpt_layout)
    # # 创建一个空白的 QWidget，并将垂直布局设置为它的布局
    # container_dpt = QWidget(window)
    # container_dpt.setLayout(dpt_layout)

    win_dpb.setWindowTitle('动盘表打印 ')
    win_dpb.setGeometry(300, 160, 300, 250)
    win_dpb.show()

# --- Thread for loading Tree3 data ---
class LoadTree3DataThread(QThread):
    data_ready = pyqtSignal(list)

    def __init__(self, checkbox3_is_checked, current_l_snapshot_for_thread, parent=None):
        super().__init__(parent)
        self.checkbox3_is_checked = checkbox3_is_checked
        self.current_l_snapshot = current_l_snapshot_for_thread
        self.worker_thread_conn = None # For thread-specific connection if needed

    def run(self):
        # It's generally safer for each thread to manage its own DB connection
        # However, sjcx_wms creates and closes its own connection.
        # For now, directly call get_chaxun_PF_PHLRMX_data
        # which in turn calls sjcx_wms.
        try:
            data = get_chaxun_PF_PHLRMX_data(self.checkbox3_is_checked, self.current_l_snapshot)
            self.data_ready.emit(data if data is not None else [])
        except Exception as e:
            print(f"Error in LoadTree3DataThread: {e}")
            traceback.print_exc()
            self.data_ready.emit([]) # Emit empty list on error
# --- End Thread for loading Tree3 data ---

# Global variable to store the currently active tree3 load thread
current_tree3_load_thread = None

# Function to handle thread cleanup
def handle_tree3_load_thread_finished():
    global current_tree3_load_thread
    # sender() returns the QObject that emitted the signal
    sender_thread = QApplication.instance().sender()
    if sender_thread:
        sender_thread.deleteLater() # Schedule the thread object for deletion
        # If the global variable still points to this thread, clear it
        if current_tree3_load_thread == sender_thread:
            current_tree3_load_thread = None

# 鼠标点选主Treeview条目动作
def on_treeview_clicked(row, column):
    global g_selected_invoice_id, current_tree3_load_thread
    l.clear()
    list_prt.clear()

    # 获取选中行的所有数据
    row_data = []
    invoice_id_from_row = None
    for col_idx in range(tree1.columnCount()):
        item = tree1.item(row, col_idx)
        text = item.text() if item else ""
        row_data.append(text)
        if col_idx == 1: # '单据编号' is the second column (index 1)
            invoice_id_from_row = text

    g_selected_invoice_id = invoice_id_from_row

    if len(row_data) > 0:
        l.extend(row_data[1:])  # 跳过序号列 ('序号' is at index 0 of row_data)

    # Populate list_prt (consider if this also needs threading if slow)
    # Original logic: l[-2] was '调度完成'. After l.extend(row_data[1:]),
    # if tree1 has 14 columns, row_data has 14 elements (0-13).
    # l has 13 elements (0-12).
    # '调度完成' is original column 13 (index 12). In row_data, it's row_data[12].
    # In l (row_data[1:]), '调度完成' is at index 11 (l[11]).
    if l and len(l) > 11 and l[11] == '是': # Check '调度完成' status (l[11])
        # Original SQL was broad, then Python filtered. Parameterize the specific query for list_prt.
        sql_list_prt = """
            SELECT wms_spkfk.spbh, PF_PHLRMX.hw, wms_spkfk.spmch, wms_spkfk.shpgg,
                   PF_PHLRMX.pihao2, PF_PHLRMX.baozhiqi, PF_PHLRMX.sxrq, wms_spkfk.shpchd,
                   PF_PHLRMX.lingsshl, wms_spkfk.dw, PF_PHLRMX.baozhshl, PF_PHLRMX.xgdjbh
            FROM PF_PHLRMX
            JOIN wms_spkfk ON PF_PHLRMX.spid = wms_spkfk.spid
            WHERE PF_PHLRMX.xgdjbh = %s
              AND (PF_PHLRMX.xgdjbh LIKE 'XSG%' OR PF_PHLRMX.xgdjbh LIKE 'JHT%') -- Retain original type check if necessary
            ORDER BY PF_PHLRMX.hw
        """
        # l[0] is '单据编号'
        result_select = sjcx_wms(sql_list_prt, (l[0],))
        if result_select:
            for res_col in result_select:
                 list_prt.append((res_col[0], res_col[1], res_col[2], res_col[3], res_col[4], res_col[5], res_col[6], res_col[7], res_col[8], res_col[9], res_col[10]))

    # Asynchronously load data for tree3
    tree3.setRowCount(0) # Clear tree3 immediately

    # We don't need to check current_tree3_load_thread.isRunning() here,
    # as it can cause a RuntimeError if the object is deleted.
    # A new thread is created for each click. The old thread's 'finished' signal
    # will trigger its cleanup via handle_tree3_load_thread_finished.

    current_l_snapshot = list(l) # Create a snapshot for the thread
    current_checkbox3_state = checkbox3.isChecked()

    # Create and start the new thread instance
    new_thread = LoadTree3DataThread(current_checkbox3_state, current_l_snapshot)
    new_thread.data_ready.connect(update_tree3_with_data)
    new_thread.finished.connect(handle_tree3_load_thread_finished) # Connect to the new handler

    # Update the global reference to the latest thread
    current_tree3_load_thread = new_thread
    current_tree3_load_thread.start()

    # 重新应用"调度完成"单元格的高亮逻辑
    # 检查当前选中行的"调度完成"状态
    if len(row_data) > 13:
        item_to_highlight = tree1.item(row, 12)  # "调度完成"单元格，列索引12
        if item_to_highlight:
            if str(row_data[12]) == '是' and str(row_data[13]) == '未打印':
                # 使用更强的绿色，并设置背景色为透明，确保在选中状态下仍然可见
                item_to_highlight.setForeground(QColor(0, 200, 0))  # 更亮的绿色
                # 设置自定义数据角色，标记这个单元格需要保持绿色高亮
                item_to_highlight.setData(Qt.UserRole, "keep_green")

def update_tree3_with_data(data):
    tree3.setRowCount(0) # Clear again just in case
    if data:
        tree3.setRowCount(len(data))
        for row_idx, row_values in enumerate(data):
            for col_idx, value_text in enumerate(row_values): # value_text should already be string
                item = QTableWidgetItem(value_text)
                item.setTextAlignment(Qt.AlignCenter)
                tree3.setItem(row_idx, col_idx, item)
                # tree3 特定颜色逻辑 (ensure row_values has enough elements)
                if len(row_values) > 15 and col_idx == 15 and value_text.strip() != '': # 'ontime' 列 (index 15 of processed_row which starts with row_idx+1)
                                                                                      # row_values comes from get_chaxun_PF_PHLRMX_data, which prepends a row number.
                                                                                      # So, the actual 'ontime' from DB would be at row_values[15] if 0-indexed from DB tuple.
                                                                                      # The processed_row in get_chaxun_PF_PHLRMX_data is `[str(row_idx + 1)] + processed_db_values`
                                                                                      # so 'ontime' (last DB field) is at `row_values[15]`
                    item_to_color = tree3.item(row_idx, 2) # 商品编号列 (index 2 of tree3, which is row_values[2])
                    if item_to_color:
                        item_to_color.setForeground(QColor(Qt.green))
    # After updating tree3, re-apply selection if it was for tree3, or simply ensure focus is reasonable.
    # For now, no specific focus management after tree3 update.

def VoiceAlert():
    # if checkbox_voice_alert.isChecked(): # 移除：此检查移至线程的run方法
    #     return # 如果禁用，则不执行后续操作

    Voice_buhuo.clear()
    Voice_diaodu.clear()
    # result_dd = sjcx_wms(
    #     event="select jzorder_hz.djbh, Wms_Mchk.dwmch, jzorder_hz.beizhu, jzorder_hz.is_gl, jzorder_hz.ywy, jzorder_hz.shenhe, jzorder_hz.is_zx, jzorder_hz.kpman, jzorder_hz.rq ,jzorder_hz.zhy, jzorder_hz.is_ht, jzorder_hz.is_phft   from jzorder_hz, Wms_Mchk    where  Wms_Mchk.dwbh = jzorder_hz.dwbh and (jzorder_hz.djbh like 'XSG%' or jzorder_hz.djbh like 'JHT%') and  jzorder_hz.is_zx='否' and rq>='" + yesterday + "'order by jzorder_hz.djbh")
    result_dd = sjcx_wms(
        event="select jzorder_hz.zhy, jzorder_hz.is_ht, jzorder_hz.is_phft   from jzorder_hz, Wms_Mchk    where  Wms_Mchk.dwbh = jzorder_hz.dwbh and (jzorder_hz.djbh like 'XSG%' or jzorder_hz.djbh like 'JHT%') and  jzorder_hz.is_zx='否' order by jzorder_hz.djbh")

    for row in result_dd:
        if row[1] != '是':
            Voice_diaodu.append(row[0])  # 未调度订单加入列表
        if row[0].strip() == '需要补货' and row[2].strip() == '否':
            Voice_buhuo.append(row[0])  # 需要补货 加入列表
    if len(Voice_diaodu) > 0:
        speak.Speak('等待调度任务' + str(len(Voice_diaodu)) + "个")
    # 只有当list_buhuo_num不为空时，才提示"等待补货任务"
    if len(Voice_buhuo) > 0 and len(list_buhuo_num) > 0:
        speak.Speak("等待补货任务" + str(len(Voice_buhuo)) + "个")
    if len(list_buhuo_num) > 0:
        speak.Speak("当前补货任务剩余" + str(len(list_buhuo_num)) + "个品种")
        # print("当前补货任务剩余" + str(len(list_buhuo_num)) + "个品种")

    # 检查是否需要提示"请检查整件复核完成情况"
    if len(list_dd_num) == 0 and len(list_PHLRHZ_num) > 0:
        # 查询PF_PHLRhz表中是否有IS_ZX为"否"的记录
        result_phlrhz_is_zx = sjcx_wms(event="select count(*) from PF_PHLRhz where ishc is null and IS_ZX='否'")
        has_unfinished = result_phlrhz_is_zx and result_phlrhz_is_zx[0][0] > 0

        # 只有当存在IS_ZX为"否"的记录时，才发出提示
        if has_unfinished:
            speak.Speak("注意！请检查整件复核完成情况")



# 修改 chaxun_dd 以返回数据而不是直接更新UI, 并接收 checkbox1 状态
def get_chaxun_dd_data(checkbox1_is_checked):
    table_data = []
    current_list_dd_num = []

    base_select_dd = """
        SELECT jz.djbh, m.dwmch, jz.beizhu, ph.is_gl,
               jz.ywy, jz.shenhe, jz.is_zx, jz.kpman, jz.rq,
               jz.zhy, jz.is_ht, jz.is_phft
        FROM jzorder_hz jz
        JOIN Wms_Mchk m ON m.dwbh = jz.dwbh
        LEFT JOIN ksoa_new..pf_PHLRhz ph ON ph.kaipiaodjbh = jz.djbh
    """
    order_by_dd = " ORDER BY jz.djbh"

    if checkbox1_is_checked:
        # 显示已执行单据：包含yesterday条件，显示所有单据
        yesterday = (dt.datetime.now() - dt.timedelta(days=1)).strftime("%Y-%m-%d")
        where_dd = " WHERE (jz.djbh LIKE 'XSG%' OR jz.djbh LIKE 'JHT%') AND jz.rq >= %s"
        sql_dd = base_select_dd + where_dd + order_by_dd
        sql_params = (yesterday,)
        result_dd_raw = sjcx_wms(sql_dd, sql_params)
    else:
        # 不显示已执行单据：不使用yesterday条件，只显示未执行的单据
        where_dd = """
            WHERE (jz.djbh LIKE 'XSG%' OR jz.djbh LIKE 'JHT%')
              AND jz.is_zx='否'
        """
        sql_dd = base_select_dd + where_dd + order_by_dd
        sql_params = ()
        result_dd_raw = sjcx_wms(sql_dd, sql_params)

    current_list_dd_num = []
    if not result_dd_raw: # Handles None or empty list
        result_dd_raw = []

    for row_idx, row_data_tuple in enumerate(result_dd_raw):
        current_list_dd_num.append(row_data_tuple[0])
        is_dy = '未打印'
        try:
            djbh_strip = str(row_data_tuple[0]).strip() if row_data_tuple[0] else ""
            kpsj_strip = str(row_data_tuple[8]).strip() if row_data_tuple[8] else ""

            file = f'\\\\*************\\wms拣货单\\拣货单{kpsj_strip}  {djbh_strip}.xlsx'
            if os.path.exists(file): # More reliable check
                 is_dy = '是'
        except FileNotFoundError: # This specific exception is unlikely with os.path.exists
            is_dy = '未打印'
        except Exception as e_file: # Catch other errors during path construction or check
            print(f"Error checking file for {djbh_strip}: {e_file}")
            is_dy = '错误'

        processed_row_values = []
        for val in row_data_tuple:
            if val is None:
                processed_row_values.append("")
            elif isinstance(val, (dt.date, dt.datetime)):
                processed_row_values.append(val.strftime('%Y-%m-%d %H:%M:%S') if isinstance(val, dt.datetime) else val.strftime('%Y-%m-%d'))
            elif isinstance(val, decimal.Decimal):
                processed_row_values.append(str(val))
            else:
                processed_row_values.append(str(val).strip())
        processed_row = [str(row_idx + 1)] + processed_row_values + [is_dy]
        table_data.append(processed_row)

    return table_data, current_list_dd_num

# 修改 chaxun_PF_PHLRHZ 以返回数据
def get_chaxun_PF_PHLRHZ_data():
    result_PF_PHLRHZ_raw = sjcx_wms(
        event="select kaipiaodjbh, beizhu, IS_ZX, is_kpxx, rq, ontime, zhy, pxjs, pxds, beizhux, ishc from PF_PHLRhz where ishc is null")

    table_data = []
    current_list_PHLRHZ_num = []
    if not result_PF_PHLRHZ_raw: result_PF_PHLRHZ_raw = []

    for row_idx, row_data_tuple in enumerate(result_PF_PHLRHZ_raw):
        current_list_PHLRHZ_num.append(row_data_tuple[0])
        processed_row_values = []
        for val in row_data_tuple:
            if val is None:
                processed_row_values.append("")
            elif isinstance(val, (dt.date, dt.datetime)):
                processed_row_values.append(val.strftime('%Y-%m-%d %H:%M:%S') if isinstance(val, dt.datetime) else val.strftime('%Y-%m-%d'))
            elif isinstance(val, decimal.Decimal):
                processed_row_values.append(str(val))
            else:
                processed_row_values.append(str(val).strip())
        processed_row = [str(row_idx + 1)] + processed_row_values
        table_data.append(processed_row)
    return table_data, current_list_PHLRHZ_num

# 修改 chaxun_PF_PHLRMX 以返回数据, 并接收 checkbox3 和 l 的状态
def get_chaxun_PF_PHLRMX_data(checkbox3_is_checked, current_l_data_snapshot):
    result_PF_PHLRMX_raw = []

    if not current_l_data_snapshot or not current_l_data_snapshot[0]: # l[0] is '单据编号'
        return [] # No selected document in tree1

    invoice_id_param = current_l_data_snapshot[0]

    # Original logic: l[-2] (which is l[11]) == '是' means '调度完成' == '是'
    # l has 13 elements if tree1 has 14 columns. l[0] to l[12].
    # '调度完成' is column 13 in tree1, so it's row_data[12], which is l[11].
    is_dispatch_complete = (len(current_l_data_snapshot) > 11 and current_l_data_snapshot[11] == '是')

    base_sql_query = """
        SELECT TOP 100 PF_PHLRMX.xgdjbh, wms_spkfk.spbh, wms_spkfk.spmch, wms_spkfk.shpgg,
               wms_spkfk.shpchd, PF_PHLRMX.hw, PF_PHLRMX.pihao2, PF_PHLRMX.lingsshl,
               wms_spkfk.dw, PF_PHLRMX.baozhshl, PF_PHLRMX.biaoshi, PF_PHLRMX.jhrq,
               PF_PHLRMX.IS_ZX, PF_PHLRMX.dayin, PF_PHLRMX.ontime
        FROM PF_PHLRMX
        INNER JOIN wms_spkfk ON PF_PHLRMX.spid = wms_spkfk.spid
        WHERE PF_PHLRMX.xgdjbh = %s
    """
    params_list = [invoice_id_param]

    if checkbox3_is_checked: # "未完成单据明细"
        base_sql_query += " AND PF_PHLRMX.IS_ZX = '否'"
    # else: # Not checkbox3_is_checked
        # The original logic was: if not checkbox3 and l[-2] == '是', then query.
        # This means if checkbox3 is false, we only query if "调度完成" is true.
        # if not is_dispatch_complete: # If not "调度完成" and checkbox3 is false, return empty
        #     return []
    # The above commented logic seems slightly off from original. Original:
    # if checkbox3_is_checked: query with IS_ZX='否'
    # else: (if not checkbox3_is_checked)
    #   if len(l) > 0 and l[-2] == '是': query with xgdjbh=l[0]
    # This means if not checkbox3, we MUST have "调度完成" == '是' to show ANY details.

    # Revised logic based on original structure:
    if checkbox3_is_checked:
        # Query for incomplete items of the selected document
        sql_final_query = base_sql_query + " AND PF_PHLRMX.IS_ZX = '否' ORDER BY PF_PHLRMX.hw"
        result_PF_PHLRMX_raw = sjcx_wms(sql_final_query, tuple(params_list))
    else: # checkbox3 is NOT checked
        if is_dispatch_complete:
            # Query for ALL items of the selected document because "调度完成" is '是'
            sql_final_query = base_sql_query + " ORDER BY PF_PHLRMX.hw" # Restored ORDER BY
            result_PF_PHLRMX_raw = sjcx_wms(sql_final_query, tuple(params_list))
        else:
            # "调度完成" is not '是', and "未完成" is not checked, so no details for tree3
            result_PF_PHLRMX_raw = []

    table_data = []
    if not result_PF_PHLRMX_raw: result_PF_PHLRMX_raw = []

    for row_idx, row_data_tuple in enumerate(result_PF_PHLRMX_raw):
        processed_row_values = []
        for val in row_data_tuple:
            if val is None:
                processed_row_values.append("")
            elif isinstance(val, (dt.date, dt.datetime)):
                processed_row_values.append(val.strftime('%Y-%m-%d %H:%M:%S') if isinstance(val, dt.datetime) else val.strftime('%Y-%m-%d'))
            elif isinstance(val, decimal.Decimal):
                processed_row_values.append(str(val))
            else:
                processed_row_values.append(str(val).strip())
        processed_row = [str(row_idx + 1)] + processed_row_values
        table_data.append(processed_row)
    return table_data

# 修改 chaxun_DBMX_KP 以返回数据
def get_chaxun_DBMX_KP_data():
    result_DBMX_KP_raw = sjcx_wms(
        event="select DBHZ_KP.djbh, DBMX_KP.recnum, wms_spkfk.spbh, wms_spkfk.spmch, DBMX_KP.miejph, DBMX_KP.drhw from DBHZ_KP, DBMX_KP, wms_spkfk where DBMX_KP.djbh = DBHZ_KP.djbh and DBMX_KP.spid = wms_spkfk.spid and DBMX_KP.is_zx = '否'")

    table_data = []
    current_list_buhuo_num = []
    if not result_DBMX_KP_raw: result_DBMX_KP_raw = []

    for row_idx, row_data_tuple in enumerate(result_DBMX_KP_raw):
        current_list_buhuo_num.append(row_data_tuple[0])
        processed_row_values = []
        for val in row_data_tuple:
            if val is None:
                processed_row_values.append("")
            elif isinstance(val, (dt.date, dt.datetime)):
                processed_row_values.append(val.strftime('%Y-%m-%d %H:%M:%S') if isinstance(val, dt.datetime) else val.strftime('%Y-%m-%d'))
            elif isinstance(val, decimal.Decimal):
                processed_row_values.append(str(val))
            else:
                processed_row_values.append(str(val).strip())
        processed_row = [str(row_idx + 1)] + processed_row_values
        table_data.append(processed_row)
    return table_data, current_list_buhuo_num

# shua 函数现在接收UI状态作为参数
def shua(checkbox1_is_checked, checkbox3_is_checked, current_l_data_snapshot):
    global list_dd_num, list_PHLRHZ_num, list_buhuo_num

    list_dd_num.clear()
    list_PHLRHZ_num.clear()
    list_buhuo_num.clear()

    data_t1, nums_t1 = get_chaxun_dd_data(checkbox1_is_checked)
    list_dd_num.extend(nums_t1)
    if not data_t1 and not checkbox1_is_checked: # 只有在非"显示已执行"且无数据时才清l
        l.clear()

    data_t2, nums_t2 = get_chaxun_PF_PHLRHZ_data()
    list_PHLRHZ_num.extend(nums_t2)

    data_t3 = get_chaxun_PF_PHLRMX_data(checkbox3_is_checked, current_l_data_snapshot)

    data_t4, nums_t4 = get_chaxun_DBMX_KP_data()
    list_buhuo_num.extend(nums_t4)

    return {
        "tree1_data": data_t1,
        "tree2_data": data_t2,
        "tree3_data": data_t3,
        "tree4_data": data_t4,
    }

# 用于在主线程更新所有表格的函数
def update_all_tables(data_dict, selected_id_to_restore=None): # Added selected_id_to_restore
    global g_selected_invoice_id # Moved global declaration to the top
    if not data_dict:
        return

    # 保存当前表格的排序状态
    tree1_sort_column = tree1.horizontalHeader().sortIndicatorSection()
    tree1_sort_order = tree1.horizontalHeader().sortIndicatorOrder()
    tree2_sort_column = tree2.horizontalHeader().sortIndicatorSection()
    tree2_sort_order = tree2.horizontalHeader().sortIndicatorOrder()
    tree3_sort_column = tree3.horizontalHeader().sortIndicatorSection()
    tree3_sort_order = tree3.horizontalHeader().sortIndicatorOrder()
    tree4_sort_column = tree4.horizontalHeader().sortIndicatorSection()
    tree4_sort_order = tree4.horizontalHeader().sortIndicatorOrder()

    # 保存当前滚动条位置
    tree1_vscroll_pos = tree1.verticalScrollBar().value()
    tree2_vscroll_pos = tree2.verticalScrollBar().value()
    tree3_vscroll_pos = tree3.verticalScrollBar().value()
    tree4_vscroll_pos = tree4.verticalScrollBar().value()

    # 暂时禁用排序功能，避免在填充数据时触发排序
    tree1.setSortingEnabled(False)
    tree2.setSortingEnabled(False)
    tree3.setSortingEnabled(False)
    tree4.setSortingEnabled(False)

    # Determine which ID to use for restoring selection in tree1
    # If selected_id_to_restore is provided (from async refresh), use it.
    # Otherwise, use the current global g_selected_invoice_id (for sync refresh or initial load).
    id_for_tree1_selection = selected_id_to_restore if selected_id_to_restore is not None else g_selected_invoice_id

    # 更新 tree1
    tree1_data = data_dict.get("tree1_data", [])
    tree1.setRowCount(0)
    tree1.setRowCount(len(tree1_data))
    restored_row_in_tree1 = False
    for row_idx, row_values in enumerate(tree1_data):
        for col_idx, value_text in enumerate(row_values):
            item = QTableWidgetItem(value_text)
            if col_idx == 2: # "客户单位"列的索引 (从0开始计数，序号为0，单据编号为1，客户单位为2)
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            else:
                item.setTextAlignment(Qt.AlignCenter)
            tree1.setItem(row_idx, col_idx, item)

            # 高亮 "备注说明" 列 (col_idx == 3) 如果其内容非空
            if col_idx == 3 and value_text.strip(): # "备注说明" 在 tree1 中是第4列 (实际数据中的第3个元素)，索引为3
                item.setForeground(QColor(65, 105, 225)) # Light Blue

        # 单独处理特定的单元格颜色，这可能会覆盖上面的浅蓝色 (如果高亮的是同一个单元格)
        # 特定颜色逻辑 for tree1 - "调度完成" 且 "未打印" 则 "调度完成"单元格绿色
        # row_values[12] is '调度完成' (is_ht), row_values[13] is '是否打印' (is_dy)
        # row_values 结构: [序号, 单据编号, ..., 需要补货(idx10), 是否调度(idx11), 调度完成(idx12), 是否打印(idx13)]
        # 注意: 这是基于原始列顺序的假设，如果 get_chaxun_dd_data 中列顺序调整，这里也需要调整。
        # 假设 `is_ht` (调度完成) 是从数据库查询结果的第11个字段 (0-indexed),
        # 在 `row_values` 中 (前面有"序号"列)，它将是 `row_values[11]`.
        # `is_dy` (是否打印) 是附加在 `row_values` 末尾的，即 `row_values[13]` (如果总共有14列数据传给tree1)。
        # `tree1` 的"调度完成"列索引是12。
        if len(row_values) > 13:
            item_to_highlight = tree1.item(row_idx, 12) # "调度完成" 单元格，列索引12
            if item_to_highlight:
                if str(row_values[11]) == '否':
                    # 将"调度完成"为"否"的单元格标记为红色
                    item_to_highlight.setForeground(QColor(255, 0, 0))  # 红色
                elif str(row_values[11]) == '是' and str(row_values[13]) == '未打印':
                    # 使用更强的绿色，确保在选中状态下仍然可见
                    item_to_highlight.setForeground(QColor(0, 200, 0))  # 更亮的绿色
                    # 设置自定义数据角色，标记这个单元格需要保持绿色高亮
                    item_to_highlight.setData(Qt.UserRole, "keep_green")


    # 更新 tree2
    tree2_data = data_dict.get("tree2_data", [])
    tree2.setRowCount(0)
    tree2.setRowCount(len(tree2_data))
    for row_idx, row_values in enumerate(tree2_data):
        for col_idx, value_text in enumerate(row_values):
            item = QTableWidgetItem(value_text)
            item.setTextAlignment(Qt.AlignCenter)
            tree2.setItem(row_idx, col_idx, item)

    # 更新 tree3
    tree3_data = data_dict.get("tree3_data", [])
    tree3.setRowCount(0)
    tree3.setRowCount(len(tree3_data))
    for row_idx, row_values in enumerate(tree3_data):
        for col_idx, value_text in enumerate(row_values):
            item = QTableWidgetItem(value_text)
            item.setTextAlignment(Qt.AlignCenter)
            tree3.setItem(row_idx, col_idx, item)
            # 特定颜色逻辑 for tree3
            # 原代码 if col_idx == 15 and value.strip() != '':
            # 现在的 row_values[15] 是 'ontime'
            # tree3.item(row_idx, 2).setForeground(QColor(Qt.green)) -> 商品编号列
            if len(row_values) > 15 and col_idx == 15 and value_text.strip() != '':
                if tree3.item(row_idx, 2): # 商品编号列
                    tree3.item(row_idx, 2).setForeground(QColor(Qt.green))

    # 更新 tree4
    tree4_data = data_dict.get("tree4_data", [])
    tree4.setRowCount(0)
    tree4.setRowCount(len(tree4_data))
    for row_idx, row_values in enumerate(tree4_data):
        for col_idx, value_text in enumerate(row_values):
            item = QTableWidgetItem(value_text)
            item.setTextAlignment(Qt.AlignCenter)
            tree4.setItem(row_idx, col_idx, item)
            item.setForeground(QColor(Qt.magenta)) # 所有单元格都品红色

    # 恢复排序功能并应用之前的排序状态
    tree1.setSortingEnabled(True)
    tree1.horizontalHeader().setSortIndicator(tree1_sort_column, tree1_sort_order)

    tree2.setSortingEnabled(True)
    tree2.horizontalHeader().setSortIndicator(tree2_sort_column, tree2_sort_order)

    tree3.setSortingEnabled(True)
    tree3.horizontalHeader().setSortIndicator(tree3_sort_column, tree3_sort_order)

    tree4.setSortingEnabled(True)
    tree4.horizontalHeader().setSortIndicator(tree4_sort_column, tree4_sort_order)

    # Restore selection in tree1 (moved after sorting for correct positioning)
    # 但不滚动到选中行，只是恢复选中状态
    if id_for_tree1_selection is not None:
        for r_idx in range(tree1.rowCount()):
            item_id_cell = tree1.item(r_idx, 1) # 单据编号在第1列 (0-indexed)
            if item_id_cell and item_id_cell.text() == id_for_tree1_selection:
                # 使用setCurrentItem代替selectRow，避免自动滚动
                tree1.setCurrentItem(tree1.item(r_idx, 0))
                # 选中整行
                for col in range(tree1.columnCount()):
                    tree1.item(r_idx, col).setSelected(True)
                restored_row_in_tree1 = True
                break

    # 恢复滚动条位置
    tree1.verticalScrollBar().setValue(tree1_vscroll_pos)
    tree2.verticalScrollBar().setValue(tree2_vscroll_pos)
    tree3.verticalScrollBar().setValue(tree3_vscroll_pos)
    tree4.verticalScrollBar().setValue(tree4_vscroll_pos)

    # If selection was restored, and tree3 load is async,
    # the on_treeview_clicked might need to be manually triggered
    # if we want tree3 to update based on the restored selection immediately.
    # For now, let's assume user will click again if they need tree3 for the restored selection.
    # Or, if no row was selected due to ID not found, clear g_selected_invoice_id
    if id_for_tree1_selection and not restored_row_in_tree1:
        if selected_id_to_restore is None: # Only clear global if it was a sync refresh using global
            # g_selected_invoice_id is already declared global at the top
            g_selected_invoice_id = None

def sjcx_erp(event):
    conn = pymssql.connect(host='***********',
                           user='sa',
                           password='xhmyy_2018',
                           database='ksoa_new',
                           charset='GBK')
    cursor = conn.cursor()
    #sql='''select * from jzorder_mx where djbh like 'XSG%' '''
    #sql =event
    cursor.execute(event)
    rs_temp = cursor.fetchall()
    return rs_temp

# 全局变量，用于在 KucunKaipiaoWindow 中共享
list_spkfk_kc = list()
list_date_kc = list()
list_date2_kc = list()

class KucunKaipiaoWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('仓储ERP小助手--进销存查询')
        self.setGeometry(100, 100, 1200, 600) # 调整了高度以便容纳控件
        # 设置窗口标志，不默认保持在最前端
        self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint)

        # 初始化共享数据列表的实例变量
        self.list_spkfk = []
        self.list_date = []
        self.list_date2 = []

        # 主布局
        main_layout = QVBoxLayout(self)

        # 顶部区域：药品搜索 和 库存概览
        top_section_layout = QHBoxLayout()

        # 左侧：药品信息 和 药品搜索
        left_panel_layout = QVBoxLayout()

        self.lab_sp_info_title = QLabel('药品信息>>') # 恢复药品信息标签
        left_panel_layout.addWidget(self.lab_sp_info_title)

        self.t2_spkfk = QTableWidget() # 药品列表
        self.t2_spkfk.setColumnCount(6)
        self.t2_spkfk.setHorizontalHeaderLabels(['药品编码', '药品编号', '药品名称', '药品规格', '生产厂家', '上市持有人'])
        self.t2_spkfk.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.t2_spkfk.setSelectionMode(QAbstractItemView.SingleSelection)
        self.t2_spkfk.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.t2_spkfk.itemClicked.connect(self.select_item_spkfk)
        header_t2 = self.t2_spkfk.horizontalHeader()
        header_t2.setSectionResizeMode(QHeaderView.ResizeToContents) # 恢复内容决定列宽
        self.t2_spkfk.setSortingEnabled(True)  # 启用表头排序功能
        left_panel_layout.addWidget(self.t2_spkfk) # 药品列表在上方

        search_sp_inner_layout = QHBoxLayout() # 内部布局
        search_sp_inner_layout.addStretch(1) # 添加前导伸展因子
        search_sp_inner_layout.addWidget(QLabel('请输入药品关键字:'))
        self.entry_his_djbh = QLineEdit()
        self.entry_his_djbh.setMaximumWidth(150)
        search_sp_inner_layout.addWidget(self.entry_his_djbh)
        self.btn_cx_sp = QPushButton('查询药品')
        self.btn_cx_sp.clicked.connect(self.chaxun_spkfk)
        search_sp_inner_layout.addWidget(self.btn_cx_sp)

        search_sp_container_widget = QWidget() # 创建容器QWidget
        search_sp_container_widget.setLayout(search_sp_inner_layout) # 将内部布局设置给QWidget
        left_panel_layout.addWidget(search_sp_container_widget, 0, Qt.AlignRight) # QWidget靠右对齐

        top_section_layout.addLayout(left_panel_layout, 2) # 左侧比例恢复为2

        # 右侧：库存信息 和 已开票未出库
        right_panel_layout = QVBoxLayout()

        self.lab_kucun_info = QLabel('库存信息>>')
        right_panel_layout.addWidget(self.lab_kucun_info)

        self.t3_kucun = QTableWidget() # 库存详情
        self.t3_kucun.setColumnCount(10)
        self.t3_kucun.setHorizontalHeaderLabels(['业务编号', '入库日期', '生产日期', '失效日期', '批次号', '库存数量', '单位', '架位号', '含税进价', '件装量'])
        self.t3_kucun.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.t3_kucun.setSelectionMode(QAbstractItemView.SingleSelection)
        self.t3_kucun.setEditTriggers(QAbstractItemView.NoEditTriggers)
        header_t3 = self.t3_kucun.horizontalHeader()
        header_t3.setSectionResizeMode(QHeaderView.ResizeToContents) # 内容决定列宽
        self.t3_kucun.setSortingEnabled(True)  # 启用表头排序功能
        right_panel_layout.addWidget(self.t3_kucun)

        # 已开票未出库信息
        kucun_options_inner_layout = QHBoxLayout()
        kucun_options_inner_layout.addStretch(1) # 添加前导伸展因子
        self.cbu_show_zero_kucun = QCheckBox('显示零库存') # 移到前面
        self.cbu_show_zero_kucun.stateChanged.connect(self.chaxun_kucun)
        kucun_options_inner_layout.addWidget(self.cbu_show_zero_kucun) # 添加到布局
        kucun_options_inner_layout.addSpacing(50) # 增加间距
        self.lab_out1 = QLabel('已开票未出库数量:')
        kucun_options_inner_layout.addWidget(self.lab_out1)
        self.lab_out2_val = QLabel('')
        self.lab_out2_val.setStyleSheet("font-weight: bold; color: red; background-color: lightblue;")
        self.lab_out2_val.setMinimumWidth(80) # 设置最小宽度
        kucun_options_inner_layout.addWidget(self.lab_out2_val)
        self.btn_wck_detail = QPushButton('查看详情')
        self.btn_wck_detail.clicked.connect(self.chaxun_wck_detail)
        kucun_options_inner_layout.addWidget(self.btn_wck_detail)

        kucun_options_container_widget = QWidget()
        kucun_options_container_widget.setLayout(kucun_options_inner_layout)
        right_panel_layout.addWidget(kucun_options_container_widget, 0, Qt.AlignRight)

        top_section_layout.addLayout(right_panel_layout, 3) # 右侧比例恢复为3

        main_layout.addLayout(top_section_layout)

        # 底部区域：采购 和 销售记录
        bottom_section_layout = QHBoxLayout()

        # 采购记录
        left_bottom_layout = QVBoxLayout()
        self.lab_cg_info = QLabel('采购信息>>')
        left_bottom_layout.addWidget(self.lab_cg_info)

        cg_filters_inner_layout = QHBoxLayout()
        cg_filters_inner_layout.addStretch(1) # 添加前导伸展因子
        cg_filters_inner_layout.addWidget(QLabel('起始时间:'))
        self.yyyy2_cg = QComboBox()
        self.mmmm2_cg = QComboBox()
        self.dddd2_cg = QComboBox()
        self.setup_date_comboboxes(self.yyyy2_cg, self.mmmm2_cg, self.dddd2_cg, years_back=2, default_past_days=365) # 默认一年
        cg_filters_inner_layout.addWidget(self.yyyy2_cg)
        cg_filters_inner_layout.addWidget(QLabel('年'))
        cg_filters_inner_layout.addWidget(self.mmmm2_cg)
        cg_filters_inner_layout.addWidget(QLabel('月'))
        cg_filters_inner_layout.addWidget(self.dddd2_cg)
        cg_filters_inner_layout.addWidget(QLabel('日'))
        self.btn_shuaxin_cg = QPushButton('采购筛选')
        self.btn_shuaxin_cg.clicked.connect(self.chaxun_cgmx)
        cg_filters_inner_layout.addWidget(self.btn_shuaxin_cg)

        cg_filters_container_widget = QWidget()
        cg_filters_container_widget.setLayout(cg_filters_inner_layout)

        self.t1_cgmx = QTableWidget() # 采购明细
        self.t1_cgmx.setColumnCount(6)
        self.t1_cgmx.setHorizontalHeaderLabels(['入库时间', '上架单号', '供货单位', '数量', '包装单位', '批号'])
        self.t1_cgmx.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.t1_cgmx.setSelectionMode(QAbstractItemView.SingleSelection)
        self.t1_cgmx.setEditTriggers(QAbstractItemView.NoEditTriggers)
        header_t1 = self.t1_cgmx.horizontalHeader()
        header_t1.setSectionResizeMode(QHeaderView.ResizeToContents)
        self.t1_cgmx.setSortingEnabled(True)  # 启用表头排序功能
        left_bottom_layout.addWidget(self.t1_cgmx) # 表格先添加
        left_bottom_layout.addWidget(cg_filters_container_widget, 0, Qt.AlignRight) # 操作组件后添加并右对齐

        bottom_section_layout.addLayout(left_bottom_layout, 2) # 底部左侧比例设为2

        # 销售记录
        right_bottom_layout = QVBoxLayout()
        self.lab_xs_info = QLabel('销售信息>>')
        right_bottom_layout.addWidget(self.lab_xs_info)

        xs_filters_inner_layout = QHBoxLayout()
        xs_filters_inner_layout.addStretch(1) # 添加前导伸展因子
        self.cbu_only_hospital = QCheckBox('只显示医院') # 移到前面
        self.cbu_only_hospital.stateChanged.connect(self.chaxun_ckmx) # 勾选也触发查询
        xs_filters_inner_layout.addWidget(self.cbu_only_hospital) # 添加到布局
        xs_filters_inner_layout.addSpacing(50) # 增加间距
        xs_filters_inner_layout.addWidget(QLabel('单位关键字:'))
        self.entry_dwname = QLineEdit()
        self.entry_dwname.setMaximumWidth(150) # 限制输入框最大宽度
        xs_filters_inner_layout.addWidget(self.entry_dwname)
        xs_filters_inner_layout.addWidget(QLabel('起始时间:'))
        self.yyyy_xs = QComboBox()
        self.mmmm_xs = QComboBox()
        self.dddd_xs = QComboBox()
        self.setup_date_comboboxes(self.yyyy_xs, self.mmmm_xs, self.dddd_xs, years_back=2, default_past_days=90) #默认3个月
        xs_filters_inner_layout.addWidget(self.yyyy_xs)
        xs_filters_inner_layout.addWidget(QLabel('年'))
        xs_filters_inner_layout.addWidget(self.mmmm_xs)
        xs_filters_inner_layout.addWidget(QLabel('月'))
        xs_filters_inner_layout.addWidget(self.dddd_xs)
        xs_filters_inner_layout.addWidget(QLabel('日'))
        self.btn_shuaxin_xs = QPushButton('出库筛选')
        self.btn_shuaxin_xs.clicked.connect(self.chaxun_ckmx)
        xs_filters_inner_layout.addWidget(self.btn_shuaxin_xs)

        xs_filters_container_widget = QWidget()
        xs_filters_container_widget.setLayout(xs_filters_inner_layout)

        self.t0_ckmx = QTableWidget() # 出库明细
        self.t0_ckmx.setColumnCount(9)
        self.t0_ckmx.setHorizontalHeaderLabels(['开票日期', '出库单号', '客户单位', '数量', '包装单位', '备注', '批号', '售价', '时间'])
        self.t0_ckmx.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.t0_ckmx.setSelectionMode(QAbstractItemView.SingleSelection)
        self.t0_ckmx.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.t0_ckmx.itemDoubleClicked.connect(self.chaxun_xsg_detail)
        header_t0 = self.t0_ckmx.horizontalHeader()
        header_t0.setSectionResizeMode(QHeaderView.ResizeToContents)
        self.t0_ckmx.setSortingEnabled(True)  # 启用表头排序功能
        right_bottom_layout.addWidget(self.t0_ckmx) # 表格先添加
        right_bottom_layout.addWidget(xs_filters_container_widget, 0, Qt.AlignRight) # 操作组件后添加并右对齐

        bottom_section_layout.addLayout(right_bottom_layout, 3) # 底部右侧比例设为3

        main_layout.addLayout(bottom_section_layout)
        self.setLayout(main_layout)

    def setup_date_comboboxes(self, y_combo, m_combo, d_combo, years_back=2, default_past_days=0):
        current_year = datetime.now().year
        y_combo.addItems([str(year) for year in range(current_year, current_year - years_back - 1, -1)])
        m_combo.addItems(['%02d' % month for month in range(1, 13)])
        d_combo.addItems(['%02d' % day for day in range(1, 32)])

        # Set maximum widths for comboboxes to keep them compact
        y_combo.setMaximumWidth(70)  # Adjusted for typical year display + dropdown arrow
        m_combo.setMaximumWidth(50)  # Adjusted for typical month display + dropdown arrow
        d_combo.setMaximumWidth(50)  # Adjusted for typical day display + dropdown arrow

        # Set default date (e.g., 90 days ago)
        default_date = datetime.now() - dt.timedelta(days=default_past_days)
        y_combo.setCurrentText(str(default_date.year))
        m_combo.setCurrentText('%02d' % default_date.month)
        d_combo.setCurrentText('%02d' % default_date.day)

        # Connect signals to update date lists and potentially re-query
        y_combo.currentIndexChanged.connect(lambda: self.update_date_list_from_combos(y_combo, m_combo, d_combo, is_cg=(y_combo==self.yyyy2_cg)))
        m_combo.currentIndexChanged.connect(lambda: self.update_date_list_from_combos(y_combo, m_combo, d_combo, is_cg=(y_combo==self.yyyy2_cg)))
        d_combo.currentIndexChanged.connect(lambda: self.update_date_list_from_combos(y_combo, m_combo, d_combo, is_cg=(y_combo==self.yyyy2_cg)))

        # Initial update of the date list
        self.update_date_list_from_combos(y_combo, m_combo, d_combo, is_cg=(y_combo==self.yyyy2_cg))

    def update_date_list_from_combos(self, y_combo, m_combo, d_combo, is_cg=False):
        year = y_combo.currentText()
        month = m_combo.currentText()
        day = d_combo.currentText()
        date_str = f"{year}-{month}-{day}"
        if is_cg:
            self.list_date2 = [date_str]
        else:
            self.list_date = [date_str]

    def chaxun_spkfk(self):
        self.lab_out2_val.setText('')
        self.clear_table(self.t2_spkfk)
        self.clear_table(self.t3_kucun)
        self.clear_table(self.t0_ckmx)
        self.clear_table(self.t1_cgmx)

        cx_input = self.entry_his_djbh.text()
        res_spkfk = sjcx_erp(
            event=f"select spid,spbh,spmch,shpgg,shpchd, ssxkcyr from spkfk where zjm  like '%{cx_input}%' or spmch like '%{cx_input}%' or spbh like '%{cx_input}%' ")

        self.t2_spkfk.setRowCount(len(res_spkfk))
        for row_idx, row_data in enumerate(res_spkfk):
            for col_idx, cell_data in enumerate(row_data):
                self.t2_spkfk.setItem(row_idx, col_idx, QTableWidgetItem(str(cell_data).strip()))

    def select_item_spkfk(self, item):
        row = item.row()
        self.list_spkfk.clear()
        spid_item = self.t2_spkfk.item(row, 0) # spid is in the first column
        if spid_item:
            self.list_spkfk.append(spid_item.text())
            # print(f"Selected spid: {self.list_spkfk[0]}")

            self.chaxun_cgmx()
            self.chaxun_ckmx()
            self.chaxun_kucun()
            self.update_wck_count() # Update unpicked count

    def update_wck_count(self):
        if not self.list_spkfk:
            self.lab_out2_val.setText('0')
            return

        total_unpicked = decimal.Decimal('0.00')
        try:
            res_jzordermx = sjcx_erp(event=f"select spid,shl from jzorder_mx where djbh  like 'xsg%' and  spid ='{self.list_spkfk[0]}' and is_zx='否'")
            for col_data in res_jzordermx:
                if col_data and len(col_data) > 1 and col_data[1] is not None:
                    try:
                        total_unpicked += decimal.Decimal(str(col_data[1]))
                    except decimal.InvalidOperation:
                        print(f"Warning: Could not convert {col_data[1]} to Decimal for unpicked count.")
            self.lab_out2_val.setText(str(total_unpicked))
        except Exception as e:
            print(f"Error updating WCK count: {e}")
            self.lab_out2_val.setText('Error')


    def chaxun_ckmx(self): # Sales records
        self.clear_table(self.t0_ckmx)
        if not self.list_spkfk: return

        dwname_input = self.entry_dwname.text()
        date_selected = bool(self.list_date) # list_date should contain the selected date from combos for XS

        # Get date from XS comboboxes
        start_date_xs_str = f"{self.yyyy_xs.currentText()}-{self.mmmm_xs.currentText()}-{self.dddd_xs.currentText()}"

        base_query = """
        SELECT {top_clause} spls_ck.rq, pf_ckhz.kaipiaodjbh, mchk.dwmch, spls_ck.chkshl, spkfk.dw,
        pf_ckhz.beizhu, pf_ckmx.pihao2, pf_ckmx.hshj, pf_ckhz.ontime
        FROM spls_ck
        JOIN mchk ON spls_ck.dwbh=mchk.dwbh
        JOIN spkfk ON spls_ck.spid=spkfk.spid
        JOIN pf_ckmx ON spls_ck.djbh=pf_ckmx.djbh AND spls_ck.spid=pf_ckmx.spid AND spls_ck.pihao=pf_ckmx.pihao AND spls_ck.hw=pf_ckmx.hw AND spls_ck.chkshl=pf_ckmx.shl
        JOIN pf_ckhz ON pf_ckmx.djbh=pf_ckhz.djbh
        WHERE spkfk.spid = '{spid}' AND spls_ck.rq >= '{start_date}'
        AND (mchk.dwmch LIKE '%{dwname}%' OR mchk.zjm LIKE '%{dwname}%')
        """
        # No TOP 200 if date is explicitly selected. If no date selected (e.g. initial load without interaction), use a sensible default.
        top_clause = "" # Always show all if a date is provided

        if self.cbu_only_hospital.isChecked():
            base_query += " AND mchk.kehufl='医院'"

        event_ckmx_str = base_query.format(
            top_clause=top_clause,
            spid=self.list_spkfk[0],
            dwname=dwname_input,
            start_date=start_date_xs_str
        ) + " ORDER BY spls_ck.rq DESC"

        res_ckmx = sjcx_erp(event=event_ckmx_str)
        self.t0_ckmx.setRowCount(len(res_ckmx))
        for r_idx, r_data in enumerate(res_ckmx):
            for c_idx, c_val in enumerate(r_data):
                val_str = str(c_val).strip() if c_val is not None else ""
                if isinstance(c_val, (date, datetime)):
                     val_str = c_val.strftime('%Y-%m-%d') if isinstance(c_val, date) else c_val.strftime('%Y-%m-%d %H:%M:%S')
                self.t0_ckmx.setItem(r_idx, c_idx, QTableWidgetItem(val_str))

    def chaxun_cgmx(self): # Purchase records
        self.clear_table(self.t1_cgmx)
        if not self.list_spkfk: return

        start_date_cg_str = f"{self.yyyy2_cg.currentText()}-{self.mmmm2_cg.currentText()}-{self.dddd2_cg.currentText()}"

        event_cgmx_str = f"""
            SELECT jh_rkmx.rq, jh_rkmx.djbh, mchk.dwmch, jh_rkmx.shl, jh_rkmx.dw, jh_rkmx.pihao
            FROM spkfk
            JOIN jh_rkmx ON spkfk.spid = jh_rkmx.spid
            JOIN jh_rkhz ON jh_rkhz.djbh = jh_rkmx.djbh
            JOIN mchk ON mchk.dwbh = jh_rkhz.dwbh
            WHERE jh_rkmx.spid = '{self.list_spkfk[0]}'
            AND (jh_rkmx.xgdjbh LIKE 'jhg%' OR jh_rkmx.xgdjbh LIKE 'jht%')
            AND jh_rkmx.rq >= '{start_date_cg_str}'
            ORDER BY jh_rkmx.rq
        """
        res_cgmx = sjcx_erp(event=event_cgmx_str)
        self.t1_cgmx.setRowCount(len(res_cgmx))
        for r_idx, r_data in enumerate(res_cgmx):
            for c_idx, c_val in enumerate(r_data):
                val_str = str(c_val).strip() if c_val is not None else ""
                if isinstance(c_val, (date, datetime)):
                     val_str = c_val.strftime('%Y-%m-%d') if isinstance(c_val, date) else c_val.strftime('%Y-%m-%d %H:%M:%S')

                if c_idx == 5: #批号列
                    k = val_str
                    j = 0
                    for char_k in k:
                        if char_k != '#': j += 1
                        else: break
                    val_str = k[j+1:] #取#之后的内容
                self.t1_cgmx.setItem(r_idx, c_idx, QTableWidgetItem(val_str))


    def chaxun_kucun(self): # Inventory details
        self.clear_table(self.t3_kucun)
        if not self.list_spkfk: return

        condition = "sphwph.shl!=0" if not self.cbu_show_zero_kucun.isChecked() else "1=1" # 1=1 means no condition on shl
        event_kucun_str = f"""
            SELECT sphwph.is_gl, sphwph.rkrq, sphwph.jlgg, sphwph.sxrq, sphwph.pihao,
                   sphwph.shl, spkfk.dw, sphwph.wmsjwh, sphwph.hshjj, sphwph.baozhiqi
            FROM spkfk
            JOIN sphwph ON spkfk.spid = sphwph.spid
            WHERE sphwph.spid = '{self.list_spkfk[0]}' AND {condition}
        """ # Note: Column order changed to match header order
        res_kucun = sjcx_erp(event=event_kucun_str)

        # Reorder for table: '业务编号'(is_gl), '入库日期'(rkrq), '生产日期'(baozhiqi), '失效日期'(sxrq), '批次号'(pihao),
        # '库存数量'(shl), '单位'(dw), '架位号'(wmsjwh), '含税进价'(hshjj), '件装量'(jlgg)
        # DB results:      is_gl,    rkrq,     jlgg,       sxrq,      pihao,   shl,    dw,      wmsjwh,   hshjj,  baozhiqi
        # Target mapping:   0 -> 0,   1 -> 1,   9 -> 2,     3 -> 3,    4 -> 4,  5 -> 5, 6 -> 6,  7 -> 7,   8 -> 8,   2 -> 9

        self.t3_kucun.setRowCount(len(res_kucun))
        for r_idx, r_data in enumerate(res_kucun):
            if r_data:
                ordered_data = [
                    r_data[0], # is_gl -> 业务编号
                    r_data[1], # rkrq -> 入库日期
                    r_data[9], # baozhiqi -> 生产日期
                    r_data[3], # sxrq -> 失效日期
                    r_data[4], # pihao -> 批次号
                    r_data[5], # shl -> 库存数量
                    r_data[6], # dw -> 单位
                    r_data[7], # wmsjwh -> 架位号
                    r_data[8], # hshjj -> 含税进价
                    r_data[2]  # jlgg -> 件装量
                ]
                for c_idx, c_val in enumerate(ordered_data):
                    val_str = str(c_val).strip() if c_val is not None else ""
                    if isinstance(c_val, (date, datetime)):
                        val_str = c_val.strftime('%Y-%m-%d') if isinstance(c_val, date) else c_val.strftime('%Y-%m-%d %H:%M:%S')
                    self.t3_kucun.setItem(r_idx, c_idx, QTableWidgetItem(val_str))

    def chaxun_wck_detail(self): # Unpicked items detail
        if not self.list_spkfk:
            QMessageBox.information(self, "提示", "请先选择一个药品。")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle('已开票未出库详情')
        dialog.setGeometry(200, 200, 700, 400)
        layout = QVBoxLayout(dialog)
        table_wck = QTableWidget()
        table_wck.setColumnCount(5)
        table_wck.setHorizontalHeaderLabels(['单据编号', '批号', '数量', '架位号', '售价'])
        header_wck = table_wck.horizontalHeader()
        header_wck.setSectionResizeMode(QHeaderView.ResizeToContents)
        layout.addWidget(table_wck)

        res_wck = sjcx_erp(event=f"select djbh,pihao2,shl,miejph,hshj from jzorder_mx where djbh like 'xsg%' and spid='{self.list_spkfk[0]}' and is_zx='否'")
        table_wck.setRowCount(len(res_wck))
        for r_idx, r_data in enumerate(res_wck):
            for c_idx, c_val in enumerate(r_data):
                val_str = str(c_val).strip() if c_val is not None else ""
                table_wck.setItem(r_idx, c_idx, QTableWidgetItem(val_str))
        dialog.exec_()

    def chaxun_xsg_detail(self, item): # Correlated picking list detail
        row = item.row()
        # '出库单号' from t0_ckmx (sales table) is the key to link to pf_ckmx.xgdjbh
        # This djbh_xgd_ref is the value from the "出库单号" column of the sales table.
        djbh_xgd_ref_item = self.t0_ckmx.item(row, 1) # Column 1 is '出库单号'
        if not djbh_xgd_ref_item:
            QMessageBox.warning(self, "错误", "无法获取选定行的出库单号。")
            return
        djbh_xgd_ref = djbh_xgd_ref_item.text().strip()

        if not djbh_xgd_ref:
            QMessageBox.information(self, "提示", "选定的出库单号为空。")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle(f'对应拣货/出库明细 - {djbh_xgd_ref}')
        dialog.setGeometry(250, 250, 850, 450)
        layout = QVBoxLayout(dialog)

        info_layout = QGridLayout()
        lab_jhd_title = QLabel("拣货单号:") #恢复原始标签
        lab_jhd_val = QLabel(djbh_xgd_ref) # This is pf_ckmx.xgdjbh
        lab_ckd_internal_title = QLabel("出库单号:") #恢复原始标签
        lab_ckd_internal_val = QLabel("N/A") # This will be pf_ckmx.djbh from query

        # 设置等宽字体和明确字号以确保单号长度显示一致
        jhd_font = QFont("Fixedsys", 12) # 拣货单号字体，字号 12
        ckd_font = QFont("Fixedsys", 13) # 出库单号字体，字号 13
        lab_jhd_val.setFont(jhd_font)
        lab_ckd_internal_val.setFont(ckd_font)

        info_layout.addWidget(lab_jhd_title, 0, 0)
        info_layout.addWidget(lab_jhd_val, 0, 1)
        info_layout.addWidget(lab_ckd_internal_title, 1, 0)
        info_layout.addWidget(lab_ckd_internal_val, 1, 1)
        layout.addLayout(info_layout)

        table_xsg = QTableWidget()
        table_xsg.setColumnCount(8)
        table_xsg.setHorizontalHeaderLabels(['架位号', '商品编码', '商品名称', '商品规格', '批号', '数量', '单位', '生产厂家'])
        header_xsg = table_xsg.horizontalHeader()
        header_xsg.setSectionResizeMode(QHeaderView.ResizeToContents) # Adjust columns to content
        layout.addWidget(table_xsg)

        # Query pf_ckmx using xgdjbh (which is djbh_xgd_ref from the sales table)
        # SQL query result columns:
        # 0: pf_ckmx.miejph (架位号)
        # 1: spkfk.spbh (商品编码)
        # 2: spkfk.spmch (商品名称)
        # 3: spkfk.shpgg (商品规格)
        # 4: pf_ckmx.pihao2 (批号)
        # 5: pf_ckmx.shl (数量)
        # 6: pf_ckmx.dw (单位)
        # 7: spkfk.shpchd (生产厂家)
        # 8: pf_ckmx.xgdjbh (拣货单号 - should match djbh_xgd_ref)
        # 9: pf_ckmx.djbh (实际出库处理单号)
        event_xsg_str = f"""
            SELECT pf_ckmx.miejph, spkfk.spbh, spkfk.spmch, spkfk.shpgg,
                   pf_ckmx.pihao2, pf_ckmx.shl, pf_ckmx.dw, spkfk.shpchd,
                   pf_ckmx.xgdjbh, pf_ckmx.djbh
            FROM pf_ckmx
            JOIN spkfk ON pf_ckmx.spid = spkfk.spid
            WHERE pf_ckmx.xgdjbh = '{djbh_xgd_ref}'
            ORDER BY pf_ckmx.miejph
        """
        res_xsg = sjcx_erp(event=event_xsg_str)

        if not res_xsg:
            QMessageBox.information(self, "提示", f"未找到拣货单号 {djbh_xgd_ref} 对应的明细记录。")
            # Keep lab_ckd_internal_val as "N/A"
        else:
            table_xsg.setRowCount(len(res_xsg))
            # Set the internal check document number from the first result row
            # (assuming it's the same for all rows of the same xgdjbh)
            first_row_data = res_xsg[0]
            if first_row_data and len(first_row_data) > 9:
                 internal_ckd_num = str(first_row_data[9]).strip() if first_row_data[9] else "N/A"
                 lab_ckd_internal_val.setText(internal_ckd_num)

            for r_idx, r_data in enumerate(res_xsg):
                # Populate table with: miejph, spbh, spmch, shpgg, pihao2, shl, dw, shpchd
                table_row_data = r_data[0:8]
                for c_idx, c_val in enumerate(table_row_data):
                    val_str = str(c_val).strip() if c_val is not None else ""
                    table_xsg.setItem(r_idx, c_idx, QTableWidgetItem(val_str))

        dialog.exec_()

    def clear_table(self, table_widget):
        table_widget.setRowCount(0)

    # 添加事件过滤器，处理窗口状态变化
    def changeEvent(self, event):
        # 当窗口状态改变时（如最小化、恢复）
        if event.type() == QEvent.WindowStateChange:
            # 只有在最小化状态时才保持在最前端
            if self.windowState() & Qt.WindowMinimized:
                # 窗口被最小化，设置保持在最前端
                self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
                self.show()
            else:
                # 窗口不是最小化状态（正常显示、最大化等），取消保持在最前端
                self.setWindowFlags(self.windowFlags() & ~Qt.WindowStaysOnTopHint)
                self.show()
        # 调用父类方法处理其他事件
        super().changeEvent(event)

    # 重写窗口关闭事件，清理全局变量引用
    def closeEvent(self, event):
        global kucun_dialog_instance
        kucun_dialog_instance = None
        event.accept()

# Function to open the KucunKaipiaoWindow
def kucunkaipiao():
    global kucun_dialog_instance
    
    # 检查是否已存在窗口实例且窗口仍然有效
    if kucun_dialog_instance is not None:
        try:
            # 检查窗口是否仍然存在且可见
            if hasattr(kucun_dialog_instance, 'isVisible') and not kucun_dialog_instance.isVisible():
                # 窗口存在但被隐藏，显示它
                kucun_dialog_instance.show()
            
            # 将窗口置于最前端并激活
            kucun_dialog_instance.raise_()
            kucun_dialog_instance.activateWindow()
            return  # 直接返回，不创建新窗口
        except (RuntimeError, AttributeError):
            # 窗口对象已被销毁或无效，清理引用
            kucun_dialog_instance = None
    
    # 创建新的窗口实例
    dialog = KucunKaipiaoWindow(window) # Pass main window as parent
    dialog.show() # Changed from exec_() to show() to make it non-modal

    # 将dialog对象保存到全局变量，防止被垃圾回收
    kucun_dialog_instance = dialog

# 电子监管工具
class UpdateDzjgGUI:
    # 定义一个类变量，用于存储更新完成后的回调函数
    on_update_completed = None

    def __init__(self, parent=None):
        self.window = QDialog(parent)
        self.window.setWindowTitle('批量更新商品电子监管标识')
        self.window.setGeometry(300, 100, 1000, 500)
        self.window.setMaximumSize(1000, 500)

        # 存储自动获取的结果
        self.set_list = []  # 需要设置的商品列表
        self.cancel_list = []  # 需要取消的商品列表

        # 创建主布局
        layout = QHBoxLayout()

        # 创建左右框架
        left_frame = QWidget()
        left_layout = QVBoxLayout(left_frame)

        right_frame = QWidget()
        right_layout = QVBoxLayout(right_frame)

        # 左侧顶部区域
        top_frame = QWidget()
        top_layout = QHBoxLayout(top_frame)

        # 第一行：输入提示
        top_layout.addWidget(QLabel('请输入商品编号(每行一个):'))
        left_layout.addWidget(top_frame)

        # 第二行：设置电子监管信息和按钮
        set_frame = QWidget()
        set_layout = QHBoxLayout(set_frame)
        self.set_label = QLabel('需要设置电子监管: 0个')
        set_layout.addWidget(self.set_label)
        self.set_button = QPushButton('添加到输入框')
        self.set_button.clicked.connect(self.add_set_list)
        self.set_button.setEnabled(False)
        set_layout.addWidget(self.set_button)
        left_layout.addWidget(set_frame)

        # 第三行：取消电子监管信息和按钮
        cancel_frame = QWidget()
        cancel_layout = QHBoxLayout(cancel_frame)
        self.cancel_label = QLabel('需要取消电子监管: 0个')
        cancel_layout.addWidget(self.cancel_label)
        self.cancel_button = QPushButton('添加到输入框')
        self.cancel_button.clicked.connect(self.add_cancel_list)
        self.cancel_button.setEnabled(False)
        cancel_layout.addWidget(self.cancel_button)
        left_layout.addWidget(cancel_frame)

        # 输入框
        self.input_text = QTextEdit()
        self.input_text.textChanged.connect(self.check_input)
        left_layout.addWidget(self.input_text)

        # 单选按钮框架
        radio_frame = QWidget()
        radio_layout = QHBoxLayout(radio_frame)

        self.dzjg_value = QButtonGroup()
        radio1 = QRadioButton('设置电子监管')
        radio2 = QRadioButton('取消电子监管')
        radio1.setChecked(True)
        self.dzjg_value.addButton(radio1, 1)
        self.dzjg_value.addButton(radio2, 2)
        radio_layout.addWidget(radio1)
        radio_layout.addWidget(radio2)

        # 执行按钮
        self.update_button = QPushButton('执行更新 >>>')
        self.update_button.clicked.connect(self.update_dzjg)
        self.update_button.setEnabled(False)
        radio_layout.addWidget(self.update_button)
        left_layout.addWidget(radio_frame)

        # 右侧结果区
        right_layout.addWidget(QLabel('执行日志:'))
        self.result_text = QTextEdit()
        right_layout.addWidget(self.result_text)

        # 添加左右框架到主布局
        layout.addWidget(left_frame)
        layout.addWidget(right_frame)

        self.window.setLayout(layout)

    def add_log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.result_text.append(f"[{timestamp}] {message}")

    def connect_db(self):
        try:
            return pymssql.connect(
                host='***********',
                user='sa',
                password='xhmyy_2018',
                database='ksoa_new',
                charset='GBK',
                as_dict=True
            )
        except Exception as e:
            self.add_log(f'数据库连接失败: {str(e)}')
            return None

    def auto_fetch(self):
        """自动获取需要更新的商品"""
        self.add_log('开始自动获取需要更新的商品...')
        try:
            # 读取追溯码文件
            UpCorpOrderList = []
            filename = "\\\\*************\\码上放心\\UpCorpOrderList.txt"
            with open(filename, "r", encoding='utf8') as f:
                for line in f:
                    if line[0] != '#' and line[0] != '$':
                        str2 = (line[21:-21]).split(',')
                        if (str2[0], str2[1]) not in UpCorpOrderList:
                            UpCorpOrderList.append((str2[0], str2[1]))

            conn = self.connect_db()
            if not conn:
                return

            cursor = conn.cursor()

            # 查询需要设置电子监管的商品
            zhuisuma_1 = []
            zhuisuma_1_1 = []
            event2 = "select a.spid, b.spbh, b.spmch, b.shpgg, SUBSTRING(a.pihao, CHARINDEX('#', a.pihao) + 1, LEN(a.pihao)) as pihao, b.shpchd, a.shl from sphwph a, spkfk b where a.spid = b.spid and a.shl!=0"
            cursor.execute(event2)
            res2 = cursor.fetchall()

            event3 = "select a.spid, b.spbh, b.spmch, b.shpgg, SUBSTRING(a.pihao, CHARINDEX('#', a.pihao) + 1, LEN(a.pihao)) as pihao, b.shpchd, a.shl from sphwph a, spkfk b where a.spid = b.spid and a.shl!=0 and b.is_dzjg = '是'"
            cursor.execute(event3)
            res3 = cursor.fetchall()

            for i in res2:
                if (i['spmch'].strip(), i['pihao']) in UpCorpOrderList:
                    zhuisuma_1.append((i['spbh'], i['spmch'].strip()))

            for i in res3:
                if (i['spmch'].strip(), i['pihao']) in UpCorpOrderList:
                    zhuisuma_1_1.append((i['spbh'], i['spmch'].strip()))

            # 需要设置电子监管的商品
            zhuisuma_not_set = []
            for col in zhuisuma_1:
                if col not in zhuisuma_1_1:
                    zhuisuma_not_set.append(col[0])  # 只取商品编号

            # 查询需要取消电子监管的商品
            event1 = "select a.spid, b.spbh, b.spmch, b.shpgg, SUBSTRING(a.pihao, CHARINDEX('#', a.pihao) + 1, LEN(a.pihao)) as pihao, b.shpchd, a.shl from sphwph a, spkfk b where a.spid = b.spid and a.shl=0 and b.is_dzjg = '是'"
            cursor.execute(event1)
            res1 = cursor.fetchall()

            zhuisuma_0 = []
            for j in res1:
                if (j['spmch'].strip(), j['pihao']) in UpCorpOrderList:
                    zhuisuma_0.append((j['spbh'], j['spmch'].strip()))

            zhuisuma_final = []
            for k in zhuisuma_0:
                if (k[0], k[1]) not in zhuisuma_1:
                    if k[0] not in zhuisuma_final:  # 去重
                        zhuisuma_final.append(k[0])  # 只取商品编号

            cursor.close()
            conn.close()

            # 更新界面显示
            self.set_list = zhuisuma_not_set
            self.cancel_list = zhuisuma_final

            # 更新统计信息
            self.set_label.setText(f'需要设置电子监管: {len(self.set_list)}个')
            self.cancel_label.setText(f'需要取消电子监管: {len(self.cancel_list)}个')

            # 启用/禁用按钮
            self.set_button.setEnabled(bool(self.set_list))
            self.cancel_button.setEnabled(bool(self.cancel_list))

            # 添加日志
            self.add_log(f'自动获取完成：')
            self.add_log(f'- 需要设置电子监管的商品：{len(self.set_list)}个')
            self.add_log(f'- 需要取消电子监管的商品：{len(self.cancel_list)}个')

        except FileNotFoundError:
            self.add_log('无法读取追溯码文件，请确认文件路径是否正确')
        except Exception as e:
            self.add_log(f'自动获取失败: {str(e)}')

    def add_set_list(self):
        """添加需要设置电子监管的商品列表到输入框"""
        self.input_text.clear()

        conn = self.connect_db()
        if not conn:
            return

        cursor = conn.cursor()
        try:
            for spbh in self.set_list:
                cursor.execute(f"select spbh,spmch,shpgg,shpchd from spkfk where spbh = '{spbh}'")
                result = cursor.fetchone()
                if result:
                    info = f"{result['spbh']} - {result['spmch'].strip()} - {result['shpgg'].strip()} - {result['shpchd'].strip()}\n"
                    self.input_text.insertPlainText(info)
                else:
                    self.input_text.insertPlainText(f"{spbh} - 未找到商品信息\n")
        finally:
            cursor.close()
            conn.close()

        self.dzjg_value.button(1).setChecked(True)
        self.add_log('已添加需要设置电子监管的商品列表到输入框')
        self.update_button.setEnabled(bool(self.set_list))

    def add_cancel_list(self):
        """添加需要取消电子监管的商品列表到输入框"""
        self.input_text.clear()

        conn = self.connect_db()
        if not conn:
            return

        cursor = conn.cursor()
        try:
            for spbh in self.cancel_list:
                cursor.execute(f"select spbh,spmch,shpgg,shpchd from spkfk where spbh = '{spbh}'")
                result = cursor.fetchone()
                if result:
                    info = f"{result['spbh']} - {result['spmch'].strip()} - {result['shpgg'].strip()} - {result['shpchd'].strip()}\n"
                    self.input_text.insertPlainText(info)
                else:
                    self.input_text.insertPlainText(f"{spbh} - 未找到商品信息\n")
        finally:
            cursor.close()
            conn.close()

        self.dzjg_value.button(2).setChecked(True)
        self.add_log('已添加需要取消电子监管的商品列表到输入框')
        self.update_button.setEnabled(bool(self.cancel_list))

    def check_input(self):
        """检查输入框内容，决定是否启用执行按钮"""
        content = self.input_text.toPlainText().strip()
        self.update_button.setEnabled(bool(content))

    def update_dzjg(self):
        """执行更新电子监管标识"""
        try:
            content = self.input_text.toPlainText().strip()
            spbh_list = [line.split('-')[0].strip() for line in content.split('\n') if line.strip()]
            spbh_list = list(set(spbh_list))  # 去重
        except Exception as e:
            self.add_log(f'错误: 处理输入数据时发生错误: {str(e)}')
            return

        if not spbh_list:
            self.add_log('警告: 请输入商品编号!')
            return

        conn = self.connect_db()
        if not conn:
            return

        cursor = conn.cursor()
        is_set = self.dzjg_value.checkedId() == 1
        update_type = "设置" if is_set else "取消"
        self.add_log(f'开始{update_type}电子监管标识...')

        try:
            for spbh in spbh_list:
                try:
                    sql = f"UPDATE spkfk SET is_dzjg='{'是' if is_set else '否'}' WHERE spbh = '{spbh}'"
                    cursor.execute(sql)
                    affected_rows = cursor.rowcount
                    self.add_log(f'商品编号 {spbh}: {update_type}电子监管 {affected_rows} 条记录')
                except Exception as e:
                    self.add_log(f'商品编号 {spbh}: 更新失败 - {str(e)}')

            conn.commit()
            self.add_log(f'批量{update_type}电子监管标识完成')

            # 执行成功后清空输入框
            self.input_text.clear()
            # 更新按钮状态
            self.update_button.setEnabled(False)

            # 重新获取数据
            self.add_log('正在刷新数据...')
            self.auto_fetch()

            # 调用更新完成回调函数，通知主界面立即刷新追溯码状态
            if UpdateDzjgGUI.on_update_completed:
                self.add_log('通知主界面刷新追溯码状态...')
                UpdateDzjgGUI.on_update_completed(bool(self.set_list) or bool(self.cancel_list))

        except Exception as e:
            conn.rollback()
            self.add_log(f'错误: 执行更新时发生错误: {str(e)}')
        finally:
            cursor.close()
            conn.close()

# 这些全局变量会在主程序中初始化
# 全局变量，用于存储追溯码监管按钮的引用
# 全局变量，用于存储是否有需要处理的追溯码

# 追溯码语音告警线程类
class ZhuisumaVoiceThread(QThread):
    # 定义信号，用于在线程中查询UI状态
    check_voice_enabled = pyqtSignal()
    voice_enabled_result = pyqtSignal(bool)

    def __init__(self, checkbox_voice_alert):
        super().__init__()
        self.stop_thread = False
        self.checkbox_voice_alert = checkbox_voice_alert
        self.last_alert_time = 0
        self.voice_enabled = True  # 默认启用语音

        # 连接信号，在主线程中查询UI状态
        self.check_voice_enabled.connect(self._check_voice_enabled_in_main_thread)
        # 连接信号，接收主线程返回的结果
        self.voice_enabled_result.connect(self._update_voice_enabled)

    def _check_voice_enabled_in_main_thread(self):
        """在主线程中检查语音是否启用"""
        try:
            # 在主线程中安全地访问UI对象
            is_checked = self.checkbox_voice_alert.isChecked()
            self.voice_enabled_result.emit(not is_checked)  # 如果勾选了"禁用语音提醒"，则返回False
        except Exception as e:
            print(f"检查语音启用状态出错: {e}")
            self.voice_enabled_result.emit(False)  # 出错时默认禁用语音

    def _update_voice_enabled(self, enabled):
        """更新语音启用状态"""
        self.voice_enabled = enabled

    def run(self):
        while not self.stop_thread:
            try:
                # 发送信号到主线程查询语音是否启用
                self.check_voice_enabled.emit()

                # 等待主线程返回结果（使用事件循环）
                QThread.msleep(100)  # 短暂等待主线程处理

                # 如果语音启用且有待处理的追溯码，则播放语音提醒
                if self.voice_enabled and has_pending_zhuisuma:
                    current_time = time.time()
                    # 每15秒提醒一次
                    if current_time - self.last_alert_time >= 15:
                        speak.Speak("注意！有追溯码需要处理")
                        self.last_alert_time = current_time

                # 短暂休眠，避免CPU占用过高
                time.sleep(1)
            except Exception as e:
                print(f"追溯码语音告警线程出错: {e}")
                time.sleep(5)  # 出错后等待5秒再继续

# 追溯码检测线程类
class ZhuisumaCheckThread(QThread):
    # 定义信号，用于在检测到状态变化时通知主线程
    status_changed = pyqtSignal(bool)

    def __init__(self):
        super().__init__()
        self.stop_thread = False

    def run(self):
        try:
            if self.stop_thread:
                return

            # 创建一个临时的UpdateDzjgGUI对象来复用其数据获取逻辑
            temp_dzjg = UpdateDzjgGUI()

            # 禁用日志输出，避免在后台线程中产生不必要的日志
            original_add_log = temp_dzjg.add_log
            temp_dzjg.add_log = lambda msg: None

            # 执行自动获取
            temp_dzjg.auto_fetch()

            # 恢复原始日志函数
            temp_dzjg.add_log = original_add_log

            if self.stop_thread:
                return

            # 检查是否有需要处理的追溯码
            has_items = bool(temp_dzjg.set_list) or bool(temp_dzjg.cancel_list)

            if not self.stop_thread:
                # 发送信号通知主线程状态
                self.status_changed.emit(has_items)

        except Exception as e:
            print(f"追溯码检测线程出错: {e}")

# 检查是否有需要处理的追溯码
def check_zhuisuma_pending():
    """启动异步线程检查是否有需要处理的追溯码"""
    global zhuisuma_check_threads

    if not zhuisuma_button:
        return

    # 清理已完成的线程
    zhuisuma_check_threads = [t for t in zhuisuma_check_threads if t.isRunning()]

    # 如果已有太多线程在运行，则跳过本次检查
    if len(zhuisuma_check_threads) >= 3:
        print("已有多个追溯码检测线程在运行，跳过本次检查")
        return

    # 创建并启动检测线程
    check_thread = ZhuisumaCheckThread()
    check_thread.status_changed.connect(update_zhuisuma_status)
    check_thread.finished.connect(lambda: clean_thread(check_thread))
    check_thread.start()

    # 将线程添加到列表中
    zhuisuma_check_threads.append(check_thread)

# 清理完成的线程
def clean_thread(thread):
    """从线程列表中移除已完成的线程"""
    global zhuisuma_check_threads
    if thread in zhuisuma_check_threads:
        zhuisuma_check_threads.remove(thread)

# 更新追溯码状态
def update_zhuisuma_status(has_items):
    """根据检测结果更新追溯码状态"""
    global has_pending_zhuisuma, zhuisuma_voice_thread

    # 更新全局状态变量
    has_pending_zhuisuma = has_items

    # 更新按钮样式
    update_zhuisuma_button_style()

    # 管理语音告警线程
    if has_pending_zhuisuma and (zhuisuma_voice_thread is None or not zhuisuma_voice_thread.isRunning()):
        # 如果有待处理的追溯码且语音告警线程未启动，则启动语音告警线程
        zhuisuma_voice_thread = ZhuisumaVoiceThread(checkbox_voice_alert)
        zhuisuma_voice_thread.start()
    elif not has_pending_zhuisuma and zhuisuma_voice_thread and zhuisuma_voice_thread.isRunning():
        # 如果没有待处理的追溯码且语音告警线程正在运行，则停止语音告警线程
        zhuisuma_voice_thread.stop_thread = True
        zhuisuma_voice_thread = None

# 更新追溯码监管按钮样式
def update_zhuisuma_button_style():
    """根据是否有待处理的追溯码更新按钮样式"""
    global has_pending_zhuisuma

    if not zhuisuma_button:
        return

    if has_pending_zhuisuma:
        # 有待处理的追溯码，设置醒目文字颜色
        zhuisuma_button.setStyleSheet("""
            QPushButton {
                color: #FF0000; /* 红色文字 */
                font-weight: bold; /* 文字加粗 */
                font-size: 14px; /* 增大字体 */
                text-decoration: underline; /* 添加下划线 */
            }
            QPushButton:hover {
                color: #FF6600; /* 悬停时橙红色 */
            }
        """)
        zhuisuma_button.setText("追溯码监管 (!)")
    else:
        # 没有待处理的追溯码，恢复默认样式
        zhuisuma_button.setStyleSheet("")
        zhuisuma_button.setText("追溯码监管")



# 调用电子监管工具
def zhuisuma_alert():
    """打开电子监管更新窗口并自动获取数据"""
    global has_pending_zhuisuma, zhuisuma_voice_thread

    # 如果语音告警线程正在运行，先暂停它
    if zhuisuma_voice_thread and zhuisuma_voice_thread.isRunning():
        zhuisuma_voice_thread.stop_thread = True
        zhuisuma_voice_thread = None

    # 显示加载提示
    loading_dialog = QMessageBox(window)
    loading_dialog.setWindowTitle("加载中")
    loading_dialog.setText("正在加载追溯码数据，请稍候...")
    loading_dialog.setStandardButtons(QMessageBox.NoButton)
    loading_dialog.show()

    # 处理Qt事件，确保加载对话框显示
    QApplication.processEvents()

    # 创建对话框
    dialog = UpdateDzjgGUI(window)

    # 设置更新完成回调函数，用于立即更新追溯码状态
    def on_dzjg_update_completed(has_items):
        global has_pending_zhuisuma
        has_pending_zhuisuma = has_items
        update_zhuisuma_button_style()

        # 管理语音告警线程
        if has_pending_zhuisuma and (zhuisuma_voice_thread is None or not zhuisuma_voice_thread.isRunning()):
            # 如果有待处理的追溯码且语音告警线程未启动，则启动语音告警线程
            new_voice_thread = ZhuisumaVoiceThread(checkbox_voice_alert)
            new_voice_thread.start()
            globals()['zhuisuma_voice_thread'] = new_voice_thread
        elif not has_pending_zhuisuma and zhuisuma_voice_thread and zhuisuma_voice_thread.isRunning():
            # 如果没有待处理的追溯码且语音告警线程正在运行，则停止语音告警线程
            zhuisuma_voice_thread.stop_thread = True
            globals()['zhuisuma_voice_thread'] = None

    # 设置回调函数
    UpdateDzjgGUI.on_update_completed = on_dzjg_update_completed

    try:
        # 在显示窗口之前先执行自动获取
        dialog.auto_fetch()

        # 关闭加载提示
        loading_dialog.accept()

        # 如果有需要设置的商品，自动添加到输入框并选中"设置电子监管"
        if dialog.set_list:
            dialog.add_set_list()
            dialog.dzjg_value.button(1).setChecked(True)
        # 如果有需要取消的商品，自动添加到输入框并选中"取消电子监管"
        elif dialog.cancel_list:
            dialog.add_cancel_list()
            dialog.dzjg_value.button(2).setChecked(True)

        # 更新状态变量
        has_pending_zhuisuma = bool(dialog.set_list) or bool(dialog.cancel_list)

        # 更新按钮样式
        update_zhuisuma_button_style()

        # 显示对话框
        dialog.window.exec_()

        # 对话框关闭后立即检查状态（不使用异步线程）
        # 创建一个临时的UpdateDzjgGUI对象来获取当前状态
        temp_dzjg = UpdateDzjgGUI()
        temp_dzjg.add_log = lambda msg: None  # 禁用日志输出
        temp_dzjg.auto_fetch()

        # 直接更新状态
        has_pending_zhuisuma = bool(temp_dzjg.set_list) or bool(temp_dzjg.cancel_list)
        update_zhuisuma_button_style()

        # 管理语音告警线程
        if has_pending_zhuisuma and (zhuisuma_voice_thread is None or not zhuisuma_voice_thread.isRunning()):
            zhuisuma_voice_thread = ZhuisumaVoiceThread(checkbox_voice_alert)
            zhuisuma_voice_thread.start()
        elif not has_pending_zhuisuma and zhuisuma_voice_thread and zhuisuma_voice_thread.isRunning():
            zhuisuma_voice_thread.stop_thread = True
            zhuisuma_voice_thread = None
    except Exception as e:
        # 关闭加载提示
        loading_dialog.accept()
        # 显示错误信息
        QMessageBox.warning(window, "错误", f"加载追溯码数据失败: {str(e)}")
        print(f"加载追溯码数据失败: {e}")


def decode_date(encoded_date):
    # 使用 base64 解码
    return base64.b64decode(encoded_date).decode('utf-8')

def check_expiration():
    file_path = r'\\*************\WMS拣货单\1\请勿删除.txt'
    if not os.path.exists(file_path):
        app = QApplication([])
        QMessageBox.critical(None, '错误', '文件缺失，脚本无法启动')
        sys.exit(1)

    with open(file_path, 'r') as f:
        encoded_date = f.read().strip()

    expiration_date_str = decode_date(encoded_date)
    expiration_date = dt.datetime.strptime(expiration_date_str, '%Y-%m-%d').date()
    current_date = dt.datetime.now().date()

    if current_date > expiration_date:
        app = QApplication([])
        QMessageBox.critical(None, 'Error', 'Script startup failed for unknown reasons!')
        sys.exit(1)

check_expiration()

# 自定义ItemDelegate类，用于保持"调度完成"列的高亮显示
class DispatchStatusDelegate(QStyledItemDelegate):
    def paint(self, painter, option, index):
        # 始终创建选项副本，避免变量未定义错误
        opt = QStyleOptionViewItem(option)

        # 获取单元格文本
        text = index.data(Qt.DisplayRole)

        # 根据单元格内容决定颜色
        if text == "否":
            # "调度完成"为"否"时始终显示红色，无论是否选中
            color = QColor(255, 0, 0)  # 红色

            # 无论单元格是否被选中，都保持红色
            # 先绘制默认背景（包括选中状态）
            super().paint(painter, opt, index)

            # 然后用红色绘制文本
            painter.save()
            painter.setPen(color)
            painter.drawText(opt.rect, Qt.AlignCenter, text if text else "")
            painter.restore()
            return

        elif text == "是" and index.data(Qt.UserRole) == "keep_green":
            # "调度完成"为"是"且有特殊标记时显示绿色
            color = QColor(0, 200, 0)  # 亮绿色

            # 无论单元格是否被选中，都保持绿色
            # 先绘制默认背景（包括选中状态）
            super().paint(painter, opt, index)

            # 然后用绿色绘制文本
            painter.save()
            painter.setPen(color)
            painter.drawText(opt.rect, Qt.AlignCenter, text if text else "")
            painter.restore()
            return
        else:
            # 默认情况下使用默认绘制
            super().paint(painter, opt, index)

row_selected = []
Voice_diaodu = []
Voice_buhuo = []
g_selected_invoice_id = None # Stores the ID of the currently selected invoice in tree1
l = list()  # 选中条目对应的汇总
# ll = list()  # 选中条目对应的明细
# lll = list()  # 整件名称和数量  货位 批号
list_prt = list()  #拣货单
list_buhuo_num = list()
list_PHLRHZ_num = list()
list_dd_num = list()
# UpCorpOrderList = list() #扫追溯码  品名 批号 列表

# 追溯码监管相关全局变量
zhuisuma_button = None  # 追溯码监管按钮引用
has_pending_zhuisuma = False  # 是否有需要处理的追溯码
zhuisuma_voice_thread = None  # 追溯码语音告警线程
zhuisuma_check_threads = []  # 存储所有的追溯码检测线程，用于程序退出时清理


# 创建应用实例
app = QApplication(sys.argv)
window = QWidget()

# 设置窗口标题和大小
window.setWindowTitle('WMS作业监控')

# --- 设置窗口图标 ---
# --- 设置窗口图标 (使用嵌入式 SVG) ---
hengmao_svg_data = r'''<svg xmlns="http://www.w3.org/2000/svg" width="42.67" height="32" viewBox="0 0 256 192"><path fill="#41CD52" d="M231.392 0H35.511L0 36.084v155.507h220.485l35.511-36.083V0z"/><path fill="#FFF" d="M95.453 33.512c16.665 0 28.357 4.789 34.988 14.453c6.719 9.575 10.034 24.38 10.034 44.418c0 13.209-1.396 23.848-4.1 31.917c-2.794 8.157-7.242 14.363-13.525 18.618l13.524 22.253l-16.665 7.891l-14.396-24.027c-2.094.62-5.323.976-9.86.976c-16.84 0-28.618-4.61-35.25-13.832c-6.63-9.22-9.946-23.848-9.946-43.709c0-19.948 3.403-34.753 10.121-44.417c6.719-9.665 18.41-14.54 35.075-14.54m84.984 6.385v23.937h22.336v15.338h-22.336v36.262c0 6.737.523 11.17 1.483 13.299c.959 2.216 3.49 3.28 7.416 3.28l13.262-.533l.785 14.364q-10.732 2.128-16.49 2.128q-13.741 0-18.847-6.384c-3.402-4.255-5.147-12.324-5.147-24.204v-38.3h-12.39v-15.25h12.39V39.897zm-84.664 8.426c-10.1 0-17.064 3.427-20.895 10.373c-3.743 6.945-5.658 17.95-5.658 33.194c0 15.153 1.828 26.068 5.484 32.562c3.657 6.495 10.709 9.832 21.069 9.832s17.325-3.247 20.981-9.651c3.57-6.405 5.398-17.319 5.398-32.563c0-15.334-1.828-26.429-5.572-33.374s-10.708-10.373-20.807-10.373"/></svg>'''

try:
    svg_bytes = hengmao_svg_data.encode('utf-8')
    renderer = QSvgRenderer(svg_bytes)

    if renderer.isValid():
        # 使用SVG的默认尺寸或者指定一个尺寸，例如32x32
        # target_size = renderer.defaultSize()
        target_size = QSize(32, 32) # 或者您期望的图标大小

        pixmap = QPixmap(target_size)
        pixmap.fill(Qt.transparent)  # 确保背景透明

        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()

        app_icon = QIcon(pixmap)
        if not app_icon.isNull():
            window.setWindowIcon(app_icon)
            app.setWindowIcon(app_icon)  # 设置应用程序图标，确保任务栏显示
            # print("成功从嵌入式SVG数据加载并设置窗口图标。") # 可选日志
        else:
            print("错误: 从渲染后的Pixmap创建QIcon失败。")
    else:
        print("错误: 嵌入式SVG数据无效，无法创建QSvgRenderer。")
        print("请检查SVG字符串内容。")
        print("另外，请确保您的Qt环境中已安装并启用了SVG支持库 (例如 libqt5svg5 或 qt5-qtsvg-plugin)。")

except ImportError:
    print("错误: 缺少 PyQt5.QtSvg 模块。请确保已安装。")
    print("您可以尝试运行: pip install PyQt5-sip PyQt5-Qt5 PyQt5-SVG")
except Exception as e:
    print(f"设置窗口嵌入式SVG图标时发生严重错误: {e}")
# --- 结束设置窗口图标 ---

window.setGeometry(300, 100, 1200, 800)    # 启动位置和大小

# 布局
layout = QGridLayout()

# 创建三个 QTableWidget 控件并添加到 QWidget 中
tree1 = QTableWidget()
tree1.setColumnCount(14)  # 设置列数
tree1.setHorizontalHeaderLabels(['序号', '单据编号', '客户单位', '备注说明', '业务编号', '业务员', '审核', '执行', '开票员', '开票时间', '需要补货', '是否调度', '调度完成', '是否打印'])
tree1.setShowGrid(True)  # 显示网格线
tree1.setAlternatingRowColors(True)  # 交替行颜色
tree1.verticalHeader().setVisible(False)  # 隐藏垂直表头
tree1.setSelectionBehavior(QAbstractItemView.SelectRows)  # 整行选择
tree1.setSelectionMode(QAbstractItemView.SingleSelection)  # 单行选择
tree1.setEditTriggers(QAbstractItemView.NoEditTriggers)  # 不可编辑

# 设置列宽
header1 = tree1.horizontalHeader()
header1.setSectionResizeMode(QHeaderView.ResizeToContents)  # 所有列根据内容调整列宽
tree1.setColumnWidth(2, 200)  # 手动设置客户单位列宽度，根据实际情况调整
header1.setDefaultAlignment(Qt.AlignCenter)  # 设置表头文字居中对齐

tree1.setSortingEnabled(True)  # 启用排序功能
tree1.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 设置垂直滚动条策略
tree1.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 设置水平滚动条策略

# 为"调度完成"列（索引12）设置自定义的ItemDelegate，以保持高亮显示
dispatch_status_delegate = DispatchStatusDelegate()
tree1.setItemDelegateForColumn(12, dispatch_status_delegate)

tree2 = QTableWidget()
tree2.setColumnCount(12)  # 设置列数
tree2.setHorizontalHeaderLabels(['序号', '单据编号',  '备注说明', '执行', '分波', '日期', '时间', 'zhy', '拼箱件数', '拼箱袋数', '是否有整', '是否回传'])
tree2.setShowGrid(True)
tree2.setAlternatingRowColors(True)
tree2.verticalHeader().setVisible(False)
tree2.setSelectionBehavior(QAbstractItemView.SelectRows)
tree2.setSelectionMode(QAbstractItemView.SingleSelection)
tree2.setEditTriggers(QAbstractItemView.NoEditTriggers)

header2 = tree2.horizontalHeader()
header2.setSectionResizeMode(QHeaderView.ResizeToContents)
header2.setDefaultAlignment(Qt.AlignCenter)
tree2.setSortingEnabled(True)
tree2.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
tree2.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

tree3 = QTableWidget()
tree3.setColumnCount(16)  # 设置列数
tree3.setHorizontalHeaderLabels(['序号', '单据编号', '商品编号', '商品名称', '商品规格', '商品产地', '架位号', '批号', '零散数量', '单位', '整件数量', '标识', '拣货日期', '复核完成', '整件复核', '完成时间'])
tree3.setShowGrid(True)
tree3.setAlternatingRowColors(True)
tree3.verticalHeader().setVisible(False)
tree3.setSelectionBehavior(QAbstractItemView.SelectRows)
tree3.setSelectionMode(QAbstractItemView.SingleSelection)
tree3.setEditTriggers(QAbstractItemView.NoEditTriggers)

header3 = tree3.horizontalHeader()
header3.setSectionResizeMode(QHeaderView.ResizeToContents)
header3.setDefaultAlignment(Qt.AlignCenter)
tree3.setSortingEnabled(True)
tree3.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
tree3.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

tree4 = QTableWidget()
tree4.setColumnCount(7)  # 设置列数
tree4.setHorizontalHeaderLabels(['序号', '单据编号', '条目', '商品编号', '商品名称', '调出货位',  '推荐货位'])
tree4.setShowGrid(True)
tree4.setAlternatingRowColors(True)
tree4.verticalHeader().setVisible(False)
tree4.setSelectionBehavior(QAbstractItemView.SelectRows)
tree4.setSelectionMode(QAbstractItemView.SingleSelection)
tree4.setEditTriggers(QAbstractItemView.NoEditTriggers)

header4 = tree4.horizontalHeader()
header4.setSectionResizeMode(QHeaderView.ResizeToContents)
header4.setDefaultAlignment(Qt.AlignCenter)
tree4.setSortingEnabled(True)
tree4.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
tree4.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

# 创建一个垂直布局，并添加多个按钮
N0_1_layout = QHBoxLayout()
N0_1_layout.addWidget(QCheckBox("显示已执行单据"))
N0_1_layout.addWidget(QCheckBox("自动刷新"))
N0_1_layout.addWidget(QCheckBox("禁用语音提醒")) # 新增：禁用语音提醒勾选框

# 创建追溯码监管按钮并保存引用
zhuisuma_button_temp = QPushButton("追溯码监管", clicked=zhuisuma_alert)
N0_1_layout.addWidget(zhuisuma_button_temp)

N0_1_layout.addWidget(QPushButton("进销存查询", clicked=kucunkaipiao))
N0_1_layout.addWidget(QPushButton("动盘表打印", clicked=dtpdb))
N0_1_layout.addWidget(QPushButton("刷新",  clicked=shua))


N0_2_layout = QHBoxLayout()
N0_2_layout.addWidget(QCheckBox("未完成单据明细"))
N0_2_layout.addWidget(QPushButton("预览",  clicked=VIEW))
N0_2_layout.addWidget(QPushButton("打开文件夹",  clicked=OPEN))
N0_2_layout.addWidget(QPushButton("打印拣货单",  clicked=PRT))


# 创建一个空白的 QWidget，并将垂直布局设置为它的布局
container1 = QWidget()
container1.setLayout(N0_1_layout)
container2 = QWidget()
container2.setLayout(N0_2_layout)


layout.addWidget(tree1, 0, 0, 1, 1)   ## 将tree1添加到布局中，第一个参数是要添加的控件，第二个参数是控件的行数，第三个参数是控件的列数，第四个参数是控件的行跨度，第五个参数是控件的列跨度
layout.addWidget(tree3, 1, 0, 1, 1)
layout.addWidget(tree2, 0, 1)
layout.addWidget(tree4, 1, 1)
layout.setColumnStretch(0, 5)
layout.setColumnStretch(1, 2)

# 将容器控件添加到布局的 (2, 0) 单元格
layout.addWidget(container1, 2, 0)
layout.addWidget(container2, 2, 1)
window.setLayout(layout)  # 为主窗口设置布局


# 获取 QHBoxLayout 中第一个控件（即 QCheckBox）的指针
checkbox1 = N0_1_layout.itemAt(0).widget()
checkbox1.setChecked(False)


checkbox2 = N0_1_layout.itemAt(1).widget()
checkbox2.setChecked(True)

checkbox_voice_alert = N0_1_layout.itemAt(2).widget() # 新增：获取禁用语音提醒勾选框的指针
checkbox_voice_alert.setChecked(False) # 新增：默认不禁用

checkbox3 = N0_2_layout.itemAt(0).widget()
checkbox3.setChecked(False)

update_thread = UpdateTreeViewThread()  # 创建TreeView更新线程实例 ,用于刷新界面，后面用checkbox2 绑定开启关闭
update_thread.start()
update_voice_thread = UpdateVoiceThread(checkbox_voice_alert)  # 修改：将勾选框对象传递给语音线程
update_voice_thread.start()

# --- Thread for Full Data Refresh ---
class FullRefreshThread(QThread):
    refresh_data_ready = pyqtSignal(dict, str) # dict for all_tables_data, str for original_selected_id

    def __init__(self, cb1_checked, cb3_checked, l_snapshot, selected_id_snapshot, parent=None):
        super().__init__(parent)
        self.cb1_checked = cb1_checked
        self.cb3_checked = cb3_checked
        self.l_snapshot = l_snapshot
        self.selected_id_snapshot = selected_id_snapshot

    def run(self):
        try:
            # print(f"FullRefreshThread: Starting shua with cb1={self.cb1_checked}, cb3={self.cb3_checked}")
            # `shua` uses `current_l_data_snapshot` which is `l_snapshot` here
            all_tables_data = shua(self.cb1_checked, self.cb3_checked, self.l_snapshot)
            # print("FullRefreshThread: shua completed.")
            self.refresh_data_ready.emit(all_tables_data if all_tables_data is not None else {}, self.selected_id_snapshot)
        except Exception as e:
            print(f"Error in FullRefreshThread: {e}")
            traceback.print_exc()
            self.refresh_data_ready.emit({}, self.selected_id_snapshot) # Emit empty on error

current_full_refresh_thread = None

def handle_full_refresh_thread_finished():
    global current_full_refresh_thread
    sender_thread = QApplication.instance().sender()
    if sender_thread:
        sender_thread.deleteLater()
        if current_full_refresh_thread == sender_thread:
            current_full_refresh_thread = None
    # print("FullRefreshThread: Finished and cleaned up.")

def handle_async_refresh_data(all_tables_data, original_selected_id):
    # print(f"MainThread: Received data from FullRefreshThread. Original selection: {original_selected_id}")
    if all_tables_data: # Check if data_dict is not empty or None
        update_all_tables(all_tables_data, selected_id_to_restore=original_selected_id)
    else:
        print("MainThread: No data received from FullRefreshThread or data was empty.")
    # Potentially, if tree3 was also part of this async refresh, update it here too.
    # For now, tree3 is handled by its own async mechanism on_treeview_clicked.

# --- Modified perform_full_refresh to be asynchronous ---
def perform_full_refresh():
    """Initiates an asynchronous full refresh of all table data."""
    global current_full_refresh_thread

    # Basic check to prevent multiple concurrent full refreshes if one is very slow
    # A more robust solution might use a QMutex or a state variable.
    if current_full_refresh_thread and current_full_refresh_thread.isRunning():
        print("Full refresh already in progress. Skipping new request.")
        return

    # print("perform_full_refresh: Initiating async full refresh.")
    s_cb1 = checkbox1.isChecked()
    s_cb3 = checkbox3.isChecked()
    s_l_snapshot = list(l)
    s_g_selected_invoice_id_snapshot = g_selected_invoice_id # Snapshot of current selection

    # Create and start the new full refresh thread
    new_full_refresh_thread = FullRefreshThread(s_cb1, s_cb3, s_l_snapshot, s_g_selected_invoice_id_snapshot)
    new_full_refresh_thread.refresh_data_ready.connect(handle_async_refresh_data)
    new_full_refresh_thread.finished.connect(handle_full_refresh_thread_finished)

    current_full_refresh_thread = new_full_refresh_thread
    current_full_refresh_thread.start()

# Connect background auto-refresh trigger to the new async perform_full_refresh
update_thread.update_signal.connect(perform_full_refresh)

# Initial load: also use the async mechanism for consistency
# This means the UI might be empty for a moment at startup until the first async refresh completes.
# If immediate population is critical, the first call could be synchronous,
# but for solving卡顿, async is key.
QTimer.singleShot(100, perform_full_refresh) # Slight delay for initial async load

# Connect UI actions to the new async perform_full_refresh
checkbox1.stateChanged.connect(perform_full_refresh)
checkbox2.stateChanged.connect(on_checkbox_stateChanged) # This only controls update_thread's run state

# Connect manual "刷新" button to the new async perform_full_refresh
refresh_button = None
for i in range(N0_1_layout.count()): # N0_1_layout 在之前已经定义
    widget = N0_1_layout.itemAt(i).widget()
    if isinstance(widget, QPushButton) and widget.text() == "刷新":
        refresh_button = widget
        break
if refresh_button:
    try:
        # 尝试断开所有连接，以防之前连接到旧的 shua
        refresh_button.clicked.disconnect()
    except TypeError:
        pass # 没有连接过或无法断开特定连接
    refresh_button.clicked.connect(perform_full_refresh)

tree1.cellClicked.connect(on_treeview_clicked) # on_treeview_clicked 内部已修改为正确传递参数

# 定义QSS样式
qss_styles = """
/* Global Font */
QWidget {
    font-family: "Segoe UI", Arial, sans-serif; /* Segoe UI first, fallback to Arial */
}

/* QPushButton */
QPushButton {
    background-color: #ADD8E6; /* 浅蓝色 LightBlue */
    border-style: solid;
    border-width: 1px;
    border-color: #A9A9A9; /* 深灰色 DarkGray, for a subtle border */
    border-radius: 5px;
    padding: 5px 10px; /* 上下5px，左右10px的内边距 */
    min-width: 70px; /* 最小宽度，确保按钮不会太小 */
    min-height: 20px; /* 最小高度 */
}

QPushButton:hover {
    background-color: #87CEEB; /* 较深的浅蓝色 SkyBlue */
    border-color: #708090; /* 石板灰 SlateGray */
}

QPushButton:pressed {
    background-color: #4682B4; /* 钢蓝色 SteelBlue */
    border-color: #2F4F4F; /* 暗石板灰 DarkSlateGray */
}

/* QTableWidget Header */
QHeaderView::section {
    background-color: #3A5F8B; /* 深蓝色，更专业的外观 */
    color: white;
    padding: 4px;
    border: 1px solid #2E4A6B; /* 深蓝色边框 */
    font-weight: bold; /* 表头文字加粗 */
    border-radius: 2px; /* 轻微圆角 */
}

/* QTableWidget */
QTableWidget {
    alternate-background-color: #f5f5f5; /* 非常浅的灰色 WhiteSmoke */
    gridline-color: #D3D3D3; /* 浅灰色 LightGray for grid lines */
    selection-background-color: rgba(176, 224, 230, 180); /* 粉蓝色 PowderBlue for selection with transparency */
    selection-color: inherit; /* 继承原始文本颜色，不覆盖单元格自定义颜色 */
}

/* Remove focus rectangle around QTableWidget and other widgets if desired */
QTableWidget:focus, QPushButton:focus, QLineEdit:focus, QComboBox:focus {
    outline: none;
}

/* Style for specific QTableWidget names if needed, but general style should apply */
/* For tree1, tree2, tree3, tree4 */
/* QTableWidget#tree1, QTableWidget#tree2, etc. if you set objectName for them */
/* For now, the generic QTableWidget and QHeaderView::section will apply. */
"""

# 应用QSS样式
app.setStyleSheet(qss_styles)

# 显示主窗口
window.show()

# 设置全局变量
zhuisuma_button = zhuisuma_button_temp

# 创建定时器，定期检查追溯码状态
zhuisuma_check_timer = QTimer()
zhuisuma_check_timer.timeout.connect(check_zhuisuma_pending)
zhuisuma_check_timer.start(60000)  # 每1分钟检查一次 (60000毫秒)，提高检查频率

# 初始检查一次追溯码状态，延迟更短时间，更快地进行第一次检查
QTimer.singleShot(5000, check_zhuisuma_pending)  # 启动5秒后检查第一次

# 程序退出时清理所有线程
def cleanup_threads():
    """程序退出时清理所有线程"""
    global zhuisuma_voice_thread, zhuisuma_check_threads

    # 停止语音告警线程
    if zhuisuma_voice_thread and zhuisuma_voice_thread.isRunning():
        zhuisuma_voice_thread.stop_thread = True
        zhuisuma_voice_thread.wait(1000)  # 等待最多1秒

    # 停止所有检测线程
    for thread in zhuisuma_check_threads:
        if thread.isRunning():
            thread.stop_thread = True
            thread.wait(1000)  # 等待最多1秒

    # 清空线程列表
    zhuisuma_check_threads.clear()

# 在应用退出前注册清理函数
app.aboutToQuit.connect(cleanup_threads)

# zhuisuma_alert() # 启动时不调用模态对话框
sys.exit(app.exec_())
